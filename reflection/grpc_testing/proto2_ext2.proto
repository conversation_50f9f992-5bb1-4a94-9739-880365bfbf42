// Copyright 2017 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto2";

option go_package = "google.golang.org/grpc/reflection/grpc_testing";

package grpc.testing;

import "reflection/grpc_testing/proto2.proto";

extend ToBeExtended {
  optional string frob = 23;
  optional AnotherExtension nitz = 29;
}

message AnotherExtension {
  optional int32 whatchamacallit = 1;
}
