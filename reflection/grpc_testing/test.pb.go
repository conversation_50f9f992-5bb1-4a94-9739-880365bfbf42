// Copyright 2017 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: reflection/grpc_testing/test.proto

package grpc_testing

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SearchResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Results       []*SearchResponse_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchResponse) Reset() {
	*x = SearchResponse{}
	mi := &file_reflection_grpc_testing_test_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResponse) ProtoMessage() {}

func (x *SearchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_reflection_grpc_testing_test_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResponse.ProtoReflect.Descriptor instead.
func (*SearchResponse) Descriptor() ([]byte, []int) {
	return file_reflection_grpc_testing_test_proto_rawDescGZIP(), []int{0}
}

func (x *SearchResponse) GetResults() []*SearchResponse_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

type SearchRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Query         string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchRequest) Reset() {
	*x = SearchRequest{}
	mi := &file_reflection_grpc_testing_test_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRequest) ProtoMessage() {}

func (x *SearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_reflection_grpc_testing_test_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRequest.ProtoReflect.Descriptor instead.
func (*SearchRequest) Descriptor() ([]byte, []int) {
	return file_reflection_grpc_testing_test_proto_rawDescGZIP(), []int{1}
}

func (x *SearchRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

type SearchResponse_Result struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Snippets      []string               `protobuf:"bytes,3,rep,name=snippets,proto3" json:"snippets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchResponse_Result) Reset() {
	*x = SearchResponse_Result{}
	mi := &file_reflection_grpc_testing_test_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchResponse_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResponse_Result) ProtoMessage() {}

func (x *SearchResponse_Result) ProtoReflect() protoreflect.Message {
	mi := &file_reflection_grpc_testing_test_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResponse_Result.ProtoReflect.Descriptor instead.
func (*SearchResponse_Result) Descriptor() ([]byte, []int) {
	return file_reflection_grpc_testing_test_proto_rawDescGZIP(), []int{0, 0}
}

func (x *SearchResponse_Result) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *SearchResponse_Result) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *SearchResponse_Result) GetSnippets() []string {
	if x != nil {
		return x.Snippets
	}
	return nil
}

var File_reflection_grpc_testing_test_proto protoreflect.FileDescriptor

const file_reflection_grpc_testing_test_proto_rawDesc = "" +
	"\n" +
	"\"reflection/grpc_testing/test.proto\x12\fgrpc.testing\"\x9d\x01\n" +
	"\x0eSearchResponse\x12=\n" +
	"\aresults\x18\x01 \x03(\v2#.grpc.testing.SearchResponse.ResultR\aresults\x1aL\n" +
	"\x06Result\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x1a\n" +
	"\bsnippets\x18\x03 \x03(\tR\bsnippets\"%\n" +
	"\rSearchRequest\x12\x14\n" +
	"\x05query\x18\x01 \x01(\tR\x05query2\xa6\x01\n" +
	"\rSearchService\x12C\n" +
	"\x06Search\x12\x1b.grpc.testing.SearchRequest\x1a\x1c.grpc.testing.SearchResponse\x12P\n" +
	"\x0fStreamingSearch\x12\x1b.grpc.testing.SearchRequest\x1a\x1c.grpc.testing.SearchResponse(\x010\x01B0Z.google.golang.org/grpc/reflection/grpc_testingb\x06proto3"

var (
	file_reflection_grpc_testing_test_proto_rawDescOnce sync.Once
	file_reflection_grpc_testing_test_proto_rawDescData []byte
)

func file_reflection_grpc_testing_test_proto_rawDescGZIP() []byte {
	file_reflection_grpc_testing_test_proto_rawDescOnce.Do(func() {
		file_reflection_grpc_testing_test_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_reflection_grpc_testing_test_proto_rawDesc), len(file_reflection_grpc_testing_test_proto_rawDesc)))
	})
	return file_reflection_grpc_testing_test_proto_rawDescData
}

var file_reflection_grpc_testing_test_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_reflection_grpc_testing_test_proto_goTypes = []any{
	(*SearchResponse)(nil),        // 0: grpc.testing.SearchResponse
	(*SearchRequest)(nil),         // 1: grpc.testing.SearchRequest
	(*SearchResponse_Result)(nil), // 2: grpc.testing.SearchResponse.Result
}
var file_reflection_grpc_testing_test_proto_depIdxs = []int32{
	2, // 0: grpc.testing.SearchResponse.results:type_name -> grpc.testing.SearchResponse.Result
	1, // 1: grpc.testing.SearchService.Search:input_type -> grpc.testing.SearchRequest
	1, // 2: grpc.testing.SearchService.StreamingSearch:input_type -> grpc.testing.SearchRequest
	0, // 3: grpc.testing.SearchService.Search:output_type -> grpc.testing.SearchResponse
	0, // 4: grpc.testing.SearchService.StreamingSearch:output_type -> grpc.testing.SearchResponse
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_reflection_grpc_testing_test_proto_init() }
func file_reflection_grpc_testing_test_proto_init() {
	if File_reflection_grpc_testing_test_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_reflection_grpc_testing_test_proto_rawDesc), len(file_reflection_grpc_testing_test_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_reflection_grpc_testing_test_proto_goTypes,
		DependencyIndexes: file_reflection_grpc_testing_test_proto_depIdxs,
		MessageInfos:      file_reflection_grpc_testing_test_proto_msgTypes,
	}.Build()
	File_reflection_grpc_testing_test_proto = out.File
	file_reflection_grpc_testing_test_proto_goTypes = nil
	file_reflection_grpc_testing_test_proto_depIdxs = nil
}
