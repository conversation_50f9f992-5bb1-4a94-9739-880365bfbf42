// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: examples/route_guide/routeguide/route_guide.proto

package routeguide

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Points are represented as latitude-longitude pairs in the E7 representation
// (degrees multiplied by 10**7 and rounded to the nearest integer).
// Latitudes should be in the range +/- 90 degrees and longitude should be in
// the range +/- 180 degrees (inclusive).
type Point struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Latitude      int32                  `protobuf:"varint,1,opt,name=latitude" json:"latitude,omitempty"`
	Longitude     int32                  `protobuf:"varint,2,opt,name=longitude" json:"longitude,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Point) Reset() {
	*x = Point{}
	mi := &file_examples_route_guide_routeguide_route_guide_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Point) ProtoMessage() {}

func (x *Point) ProtoReflect() protoreflect.Message {
	mi := &file_examples_route_guide_routeguide_route_guide_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Point.ProtoReflect.Descriptor instead.
func (*Point) Descriptor() ([]byte, []int) {
	return file_examples_route_guide_routeguide_route_guide_proto_rawDescGZIP(), []int{0}
}

func (x *Point) GetLatitude() int32 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *Point) GetLongitude() int32 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

// A latitude-longitude rectangle, represented as two diagonally opposite
// points "lo" and "hi".
type Rectangle struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// One corner of the rectangle.
	Lo *Point `protobuf:"bytes,1,opt,name=lo" json:"lo,omitempty"`
	// The other corner of the rectangle.
	Hi            *Point `protobuf:"bytes,2,opt,name=hi" json:"hi,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Rectangle) Reset() {
	*x = Rectangle{}
	mi := &file_examples_route_guide_routeguide_route_guide_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Rectangle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rectangle) ProtoMessage() {}

func (x *Rectangle) ProtoReflect() protoreflect.Message {
	mi := &file_examples_route_guide_routeguide_route_guide_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rectangle.ProtoReflect.Descriptor instead.
func (*Rectangle) Descriptor() ([]byte, []int) {
	return file_examples_route_guide_routeguide_route_guide_proto_rawDescGZIP(), []int{1}
}

func (x *Rectangle) GetLo() *Point {
	if x != nil {
		return x.Lo
	}
	return nil
}

func (x *Rectangle) GetHi() *Point {
	if x != nil {
		return x.Hi
	}
	return nil
}

// A feature names something at a given point.
//
// If a feature could not be named, the name is empty.
type Feature struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the feature.
	Name string `protobuf:"bytes,1,opt,name=name" json:"name,omitempty"`
	// The point where the feature is detected.
	Location      *Point `protobuf:"bytes,2,opt,name=location" json:"location,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Feature) Reset() {
	*x = Feature{}
	mi := &file_examples_route_guide_routeguide_route_guide_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Feature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Feature) ProtoMessage() {}

func (x *Feature) ProtoReflect() protoreflect.Message {
	mi := &file_examples_route_guide_routeguide_route_guide_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Feature.ProtoReflect.Descriptor instead.
func (*Feature) Descriptor() ([]byte, []int) {
	return file_examples_route_guide_routeguide_route_guide_proto_rawDescGZIP(), []int{2}
}

func (x *Feature) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Feature) GetLocation() *Point {
	if x != nil {
		return x.Location
	}
	return nil
}

// A RouteNote is a message sent while at a given point.
type RouteNote struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The location from which the message is sent.
	Location *Point `protobuf:"bytes,1,opt,name=location" json:"location,omitempty"`
	// The message to be sent.
	Message       string `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RouteNote) Reset() {
	*x = RouteNote{}
	mi := &file_examples_route_guide_routeguide_route_guide_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RouteNote) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteNote) ProtoMessage() {}

func (x *RouteNote) ProtoReflect() protoreflect.Message {
	mi := &file_examples_route_guide_routeguide_route_guide_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteNote.ProtoReflect.Descriptor instead.
func (*RouteNote) Descriptor() ([]byte, []int) {
	return file_examples_route_guide_routeguide_route_guide_proto_rawDescGZIP(), []int{3}
}

func (x *RouteNote) GetLocation() *Point {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *RouteNote) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// A RouteSummary is received in response to a RecordRoute rpc.
//
// It contains the number of individual points received, the number of
// detected features, and the total distance covered as the cumulative sum of
// the distance between each point.
type RouteSummary struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The number of points received.
	PointCount int32 `protobuf:"varint,1,opt,name=point_count,json=pointCount" json:"point_count,omitempty"`
	// The number of known features passed while traversing the route.
	FeatureCount int32 `protobuf:"varint,2,opt,name=feature_count,json=featureCount" json:"feature_count,omitempty"`
	// The distance covered in metres.
	Distance int32 `protobuf:"varint,3,opt,name=distance" json:"distance,omitempty"`
	// The duration of the traversal in seconds.
	ElapsedTime   int32 `protobuf:"varint,4,opt,name=elapsed_time,json=elapsedTime" json:"elapsed_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RouteSummary) Reset() {
	*x = RouteSummary{}
	mi := &file_examples_route_guide_routeguide_route_guide_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RouteSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteSummary) ProtoMessage() {}

func (x *RouteSummary) ProtoReflect() protoreflect.Message {
	mi := &file_examples_route_guide_routeguide_route_guide_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteSummary.ProtoReflect.Descriptor instead.
func (*RouteSummary) Descriptor() ([]byte, []int) {
	return file_examples_route_guide_routeguide_route_guide_proto_rawDescGZIP(), []int{4}
}

func (x *RouteSummary) GetPointCount() int32 {
	if x != nil {
		return x.PointCount
	}
	return 0
}

func (x *RouteSummary) GetFeatureCount() int32 {
	if x != nil {
		return x.FeatureCount
	}
	return 0
}

func (x *RouteSummary) GetDistance() int32 {
	if x != nil {
		return x.Distance
	}
	return 0
}

func (x *RouteSummary) GetElapsedTime() int32 {
	if x != nil {
		return x.ElapsedTime
	}
	return 0
}

var File_examples_route_guide_routeguide_route_guide_proto protoreflect.FileDescriptor

const file_examples_route_guide_routeguide_route_guide_proto_rawDesc = "" +
	"\n" +
	"1examples/route_guide/routeguide/route_guide.proto\x12\n" +
	"routeguide\"A\n" +
	"\x05Point\x12\x1a\n" +
	"\blatitude\x18\x01 \x01(\x05R\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x02 \x01(\x05R\tlongitude\"Q\n" +
	"\tRectangle\x12!\n" +
	"\x02lo\x18\x01 \x01(\v2\x11.routeguide.PointR\x02lo\x12!\n" +
	"\x02hi\x18\x02 \x01(\v2\x11.routeguide.PointR\x02hi\"L\n" +
	"\aFeature\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12-\n" +
	"\blocation\x18\x02 \x01(\v2\x11.routeguide.PointR\blocation\"T\n" +
	"\tRouteNote\x12-\n" +
	"\blocation\x18\x01 \x01(\v2\x11.routeguide.PointR\blocation\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x93\x01\n" +
	"\fRouteSummary\x12\x1f\n" +
	"\vpoint_count\x18\x01 \x01(\x05R\n" +
	"pointCount\x12#\n" +
	"\rfeature_count\x18\x02 \x01(\x05R\ffeatureCount\x12\x1a\n" +
	"\bdistance\x18\x03 \x01(\x05R\bdistance\x12!\n" +
	"\felapsed_time\x18\x04 \x01(\x05R\velapsedTime2\x85\x02\n" +
	"\n" +
	"RouteGuide\x126\n" +
	"\n" +
	"GetFeature\x12\x11.routeguide.Point\x1a\x13.routeguide.Feature\"\x00\x12>\n" +
	"\fListFeatures\x12\x15.routeguide.Rectangle\x1a\x13.routeguide.Feature\"\x000\x01\x12>\n" +
	"\vRecordRoute\x12\x11.routeguide.Point\x1a\x18.routeguide.RouteSummary\"\x00(\x01\x12?\n" +
	"\tRouteChat\x12\x15.routeguide.RouteNote\x1a\x15.routeguide.RouteNote\"\x00(\x010\x01Bm\n" +
	"\x1bio.grpc.examples.routeguideB\x0fRouteGuideProtoP\x01Z6google.golang.org/grpc/examples/route_guide/routeguide\x92\x03\x02\b\x02b\beditionsp\xe8\a"

var (
	file_examples_route_guide_routeguide_route_guide_proto_rawDescOnce sync.Once
	file_examples_route_guide_routeguide_route_guide_proto_rawDescData []byte
)

func file_examples_route_guide_routeguide_route_guide_proto_rawDescGZIP() []byte {
	file_examples_route_guide_routeguide_route_guide_proto_rawDescOnce.Do(func() {
		file_examples_route_guide_routeguide_route_guide_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_examples_route_guide_routeguide_route_guide_proto_rawDesc), len(file_examples_route_guide_routeguide_route_guide_proto_rawDesc)))
	})
	return file_examples_route_guide_routeguide_route_guide_proto_rawDescData
}

var file_examples_route_guide_routeguide_route_guide_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_examples_route_guide_routeguide_route_guide_proto_goTypes = []any{
	(*Point)(nil),        // 0: routeguide.Point
	(*Rectangle)(nil),    // 1: routeguide.Rectangle
	(*Feature)(nil),      // 2: routeguide.Feature
	(*RouteNote)(nil),    // 3: routeguide.RouteNote
	(*RouteSummary)(nil), // 4: routeguide.RouteSummary
}
var file_examples_route_guide_routeguide_route_guide_proto_depIdxs = []int32{
	0, // 0: routeguide.Rectangle.lo:type_name -> routeguide.Point
	0, // 1: routeguide.Rectangle.hi:type_name -> routeguide.Point
	0, // 2: routeguide.Feature.location:type_name -> routeguide.Point
	0, // 3: routeguide.RouteNote.location:type_name -> routeguide.Point
	0, // 4: routeguide.RouteGuide.GetFeature:input_type -> routeguide.Point
	1, // 5: routeguide.RouteGuide.ListFeatures:input_type -> routeguide.Rectangle
	0, // 6: routeguide.RouteGuide.RecordRoute:input_type -> routeguide.Point
	3, // 7: routeguide.RouteGuide.RouteChat:input_type -> routeguide.RouteNote
	2, // 8: routeguide.RouteGuide.GetFeature:output_type -> routeguide.Feature
	2, // 9: routeguide.RouteGuide.ListFeatures:output_type -> routeguide.Feature
	4, // 10: routeguide.RouteGuide.RecordRoute:output_type -> routeguide.RouteSummary
	3, // 11: routeguide.RouteGuide.RouteChat:output_type -> routeguide.RouteNote
	8, // [8:12] is the sub-list for method output_type
	4, // [4:8] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_examples_route_guide_routeguide_route_guide_proto_init() }
func file_examples_route_guide_routeguide_route_guide_proto_init() {
	if File_examples_route_guide_routeguide_route_guide_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_examples_route_guide_routeguide_route_guide_proto_rawDesc), len(file_examples_route_guide_routeguide_route_guide_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_examples_route_guide_routeguide_route_guide_proto_goTypes,
		DependencyIndexes: file_examples_route_guide_routeguide_route_guide_proto_depIdxs,
		MessageInfos:      file_examples_route_guide_routeguide_route_guide_proto_msgTypes,
	}.Build()
	File_examples_route_guide_routeguide_route_guide_proto = out.File
	file_examples_route_guide_routeguide_route_guide_proto_goTypes = nil
	file_examples_route_guide_routeguide_route_guide_proto_depIdxs = nil
}
