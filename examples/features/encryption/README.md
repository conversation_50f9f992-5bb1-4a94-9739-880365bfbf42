# Encryption

The example for encryption includes three individual examples for TLS, ALTS
and mTLS encryption mechanism respectively.

## Try it

In each example's subdirectory:

```
go run server/main.go
```

```
go run client/main.go
```

## Explanation

### TLS

TLS is a commonly used cryptographic protocol to provide end-to-end
communication security. In the example, we show how to set up a server
authenticated TLS connection to transmit RPC.

In our `grpc/credentials` package, we provide several convenience methods to
create grpc
[`credentials.TransportCredentials`](https://godoc.org/google.golang.org/grpc/credentials#TransportCredentials)
base on TLS. Refer to the
[godoc](https://godoc.org/google.golang.org/grpc/credentials) for details.

In our example, we use the public/private keys created ahead:
* "server_cert.pem" contains the server certificate (public key).
* "server_key.pem" contains the server private key.
* "ca_cert.pem" contains the certificate (certificate authority)
that can verify the server's certificate.

On server side, we provide the paths to "server_cert.pem" and "server_key.pem" to
configure TLS and create the server credential using
[`credentials.NewServerTLSFromFile`](https://godoc.org/google.golang.org/grpc/credentials#NewServerTLSFromFile).

On client side, we provide the path to the "ca_cert.pem" to configure TLS and create
the client credential using
[`credentials.NewClientTLSFromFile`](https://godoc.org/google.golang.org/grpc/credentials#NewClientTLSFromFile).
Note that we override the server name with "x.test.example.com", as the server
certificate is valid for *.test.example.com but not localhost. It is solely for
the convenience of making an example.

Once the credentials have been created at both sides, we can start the server
with the just created server credential (by calling
[`grpc.Creds`](https://godoc.org/google.golang.org/grpc#Creds)) and let client dial
to the server with the created client credential (by calling
[`grpc.WithTransportCredentials`](https://godoc.org/google.golang.org/grpc#WithTransportCredentials))

And finally we make an RPC call over the created `grpc.ClientConn` to test the secure
connection based upon TLS is successfully up.

### ALTS
NOTE: ALTS currently needs special early access permission on GCP. You can ask
about the detailed process in https://groups.google.com/forum/#!forum/grpc-io.

ALTS is the Google's Application Layer Transport Security, which supports mutual
authentication and transport encryption. Note that ALTS is currently only
supported on Google Cloud Platform, and therefore you can only run the example
successfully in a GCP environment. In our example, we show how to initiate a
secure connection that is based on ALTS.

Unlike TLS, ALTS makes certificate/key management transparent to user. So it is
easier to set up.

On server side, first call
[`alts.DefaultServerOptions`](https://godoc.org/google.golang.org/grpc/credentials/alts#DefaultServerOptions)
to get the configuration for alts and then provide the configuration to
[`alts.NewServerCreds`](https://godoc.org/google.golang.org/grpc/credentials/alts#NewServerCreds)
to create the server credential based upon alts.

On client side, first call
[`alts.DefaultClientOptions`](https://godoc.org/google.golang.org/grpc/credentials/alts#DefaultClientOptions)
to get the configuration for alts and then provide the configuration to
[`alts.NewClientCreds`](https://godoc.org/google.golang.org/grpc/credentials/alts#NewClientCreds)
to create the client credential based upon alts.

Next, same as TLS, start the server with the server credential and let client
dial to server with the client credential.

Finally, make an RPC to test the secure connection based upon ALTS is
successfully up.

### mTLS

In mutual TLS (mTLS), the client and the server authenticate each other. gRPC
allows users to configure mutual TLS at the connection level.

In this example, we use the following public/private keys created ahead of time:

* "server_cert.pem" contains the server's certificate (public key).
* "server_key.pem" contains the server's private key.
* "ca_cert.pem" contains the certificate of the certificate authority that can
  verify the server's certificate.
* "client_cert.pem" contains the client's certificate (public key).
* "client_key.pem" contains the client's private key.
* "client_ca_cert.pem" contains the certificate of the certificate authority
  that can verify the client's certificate.

In normal TLS, the server is only concerned with presenting the server
certificate for clients to verify. In mutual TLS, the server also loads in a
list of trusted CA files for verifying the client's presented certificates.
This is done by setting
[`tls.Config.ClientCAs`](https://pkg.go.dev/crypto/tls#Config.ClientCAs)
to the list of trusted CA files,
and setting
[`tls.config.ClientAuth`](https://pkg.go.dev/crypto/tls#Config.ClientAuth)
to
[`tls.RequireAndVerifyClientCert`](https://pkg.go.dev/crypto/tls#RequireAndVerifyClientCert).

In normal TLS, the client is only concerned with authenticating the server by
using one or more trusted CA file. In mutual TLS, the client also presents its
client certificate to the server for authentication. This is done by setting
[`tls.Config.Certificates`](https://pkg.go.dev/crypto/tls#Config.Certificates).
