[req]
distinguished_name  = req_distinguished_name
req_extensions     = v3_req

[req_distinguished_name]
countryName           = Country Name (2 letter code)
countryName_default   = US
stateOrProvinceName   = State or Province Name (full name)
stateOrProvinceName_default = Georgia
localityName          = Locality Name (eg, city)
localityName_default  = Atlanta
organizationName          = Organization Name (eg, company)
organizationName_default  = Test Department
commonName            = Common Name (eg, YOUR name)
commonName_max        = 64

[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
IP.1 = 0.0.0.0
