// Copyright 2018 The gRPC Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// The canonical version of this proto can be found at
// https://github.com/grpc/grpc-proto/blob/master/grpc/gcp/handshaker.proto

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: grpc/gcp/handshaker.proto

package grpc_gcp

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HandshakeProtocol int32

const (
	// Default value.
	HandshakeProtocol_HANDSHAKE_PROTOCOL_UNSPECIFIED HandshakeProtocol = 0
	// TLS handshake protocol.
	HandshakeProtocol_TLS HandshakeProtocol = 1
	// Application Layer Transport Security handshake protocol.
	HandshakeProtocol_ALTS HandshakeProtocol = 2
)

// Enum value maps for HandshakeProtocol.
var (
	HandshakeProtocol_name = map[int32]string{
		0: "HANDSHAKE_PROTOCOL_UNSPECIFIED",
		1: "TLS",
		2: "ALTS",
	}
	HandshakeProtocol_value = map[string]int32{
		"HANDSHAKE_PROTOCOL_UNSPECIFIED": 0,
		"TLS":                            1,
		"ALTS":                           2,
	}
)

func (x HandshakeProtocol) Enum() *HandshakeProtocol {
	p := new(HandshakeProtocol)
	*p = x
	return p
}

func (x HandshakeProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HandshakeProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_gcp_handshaker_proto_enumTypes[0].Descriptor()
}

func (HandshakeProtocol) Type() protoreflect.EnumType {
	return &file_grpc_gcp_handshaker_proto_enumTypes[0]
}

func (x HandshakeProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HandshakeProtocol.Descriptor instead.
func (HandshakeProtocol) EnumDescriptor() ([]byte, []int) {
	return file_grpc_gcp_handshaker_proto_rawDescGZIP(), []int{0}
}

type NetworkProtocol int32

const (
	NetworkProtocol_NETWORK_PROTOCOL_UNSPECIFIED NetworkProtocol = 0
	NetworkProtocol_TCP                          NetworkProtocol = 1
	NetworkProtocol_UDP                          NetworkProtocol = 2
)

// Enum value maps for NetworkProtocol.
var (
	NetworkProtocol_name = map[int32]string{
		0: "NETWORK_PROTOCOL_UNSPECIFIED",
		1: "TCP",
		2: "UDP",
	}
	NetworkProtocol_value = map[string]int32{
		"NETWORK_PROTOCOL_UNSPECIFIED": 0,
		"TCP":                          1,
		"UDP":                          2,
	}
)

func (x NetworkProtocol) Enum() *NetworkProtocol {
	p := new(NetworkProtocol)
	*p = x
	return p
}

func (x NetworkProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworkProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_gcp_handshaker_proto_enumTypes[1].Descriptor()
}

func (NetworkProtocol) Type() protoreflect.EnumType {
	return &file_grpc_gcp_handshaker_proto_enumTypes[1]
}

func (x NetworkProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworkProtocol.Descriptor instead.
func (NetworkProtocol) EnumDescriptor() ([]byte, []int) {
	return file_grpc_gcp_handshaker_proto_rawDescGZIP(), []int{1}
}

type Endpoint struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// IP address. It should contain an IPv4 or IPv6 string literal, e.g.
	// "***********" or "2001:db8::1".
	IpAddress string `protobuf:"bytes,1,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	// Port number.
	Port int32 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	// Network protocol (e.g., TCP, UDP) associated with this endpoint.
	Protocol      NetworkProtocol `protobuf:"varint,3,opt,name=protocol,proto3,enum=grpc.gcp.NetworkProtocol" json:"protocol,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Endpoint) Reset() {
	*x = Endpoint{}
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Endpoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Endpoint) ProtoMessage() {}

func (x *Endpoint) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Endpoint.ProtoReflect.Descriptor instead.
func (*Endpoint) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_handshaker_proto_rawDescGZIP(), []int{0}
}

func (x *Endpoint) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *Endpoint) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Endpoint) GetProtocol() NetworkProtocol {
	if x != nil {
		return x.Protocol
	}
	return NetworkProtocol_NETWORK_PROTOCOL_UNSPECIFIED
}

type Identity struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to IdentityOneof:
	//
	//	*Identity_ServiceAccount
	//	*Identity_Hostname
	IdentityOneof isIdentity_IdentityOneof `protobuf_oneof:"identity_oneof"`
	// Additional attributes of the identity.
	Attributes    map[string]string `protobuf:"bytes,3,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Identity) Reset() {
	*x = Identity{}
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Identity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Identity) ProtoMessage() {}

func (x *Identity) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Identity.ProtoReflect.Descriptor instead.
func (*Identity) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_handshaker_proto_rawDescGZIP(), []int{1}
}

func (x *Identity) GetIdentityOneof() isIdentity_IdentityOneof {
	if x != nil {
		return x.IdentityOneof
	}
	return nil
}

func (x *Identity) GetServiceAccount() string {
	if x != nil {
		if x, ok := x.IdentityOneof.(*Identity_ServiceAccount); ok {
			return x.ServiceAccount
		}
	}
	return ""
}

func (x *Identity) GetHostname() string {
	if x != nil {
		if x, ok := x.IdentityOneof.(*Identity_Hostname); ok {
			return x.Hostname
		}
	}
	return ""
}

func (x *Identity) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

type isIdentity_IdentityOneof interface {
	isIdentity_IdentityOneof()
}

type Identity_ServiceAccount struct {
	// Service account of a connection endpoint.
	ServiceAccount string `protobuf:"bytes,1,opt,name=service_account,json=serviceAccount,proto3,oneof"`
}

type Identity_Hostname struct {
	// Hostname of a connection endpoint.
	Hostname string `protobuf:"bytes,2,opt,name=hostname,proto3,oneof"`
}

func (*Identity_ServiceAccount) isIdentity_IdentityOneof() {}

func (*Identity_Hostname) isIdentity_IdentityOneof() {}

type StartClientHandshakeReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Handshake security protocol requested by the client.
	HandshakeSecurityProtocol HandshakeProtocol `protobuf:"varint,1,opt,name=handshake_security_protocol,json=handshakeSecurityProtocol,proto3,enum=grpc.gcp.HandshakeProtocol" json:"handshake_security_protocol,omitempty"`
	// The application protocols supported by the client, e.g., "h2" (for http2),
	// "grpc".
	ApplicationProtocols []string `protobuf:"bytes,2,rep,name=application_protocols,json=applicationProtocols,proto3" json:"application_protocols,omitempty"`
	// The record protocols supported by the client, e.g.,
	// "ALTSRP_GCM_AES128".
	RecordProtocols []string `protobuf:"bytes,3,rep,name=record_protocols,json=recordProtocols,proto3" json:"record_protocols,omitempty"`
	// (Optional) Describes which server identities are acceptable by the client.
	// If target identities are provided and none of them matches the peer
	// identity of the server, handshake will fail.
	TargetIdentities []*Identity `protobuf:"bytes,4,rep,name=target_identities,json=targetIdentities,proto3" json:"target_identities,omitempty"`
	// (Optional) Application may specify a local identity. Otherwise, the
	// handshaker chooses a default local identity.
	LocalIdentity *Identity `protobuf:"bytes,5,opt,name=local_identity,json=localIdentity,proto3" json:"local_identity,omitempty"`
	// (Optional) Local endpoint information of the connection to the server,
	// such as local IP address, port number, and network protocol.
	LocalEndpoint *Endpoint `protobuf:"bytes,6,opt,name=local_endpoint,json=localEndpoint,proto3" json:"local_endpoint,omitempty"`
	// (Optional) Endpoint information of the remote server, such as IP address,
	// port number, and network protocol.
	RemoteEndpoint *Endpoint `protobuf:"bytes,7,opt,name=remote_endpoint,json=remoteEndpoint,proto3" json:"remote_endpoint,omitempty"`
	// (Optional) If target name is provided, a secure naming check is performed
	// to verify that the peer authenticated identity is indeed authorized to run
	// the target name.
	TargetName string `protobuf:"bytes,8,opt,name=target_name,json=targetName,proto3" json:"target_name,omitempty"`
	// (Optional) RPC protocol versions supported by the client.
	RpcVersions *RpcProtocolVersions `protobuf:"bytes,9,opt,name=rpc_versions,json=rpcVersions,proto3" json:"rpc_versions,omitempty"`
	// (Optional) Maximum frame size supported by the client.
	MaxFrameSize uint32 `protobuf:"varint,10,opt,name=max_frame_size,json=maxFrameSize,proto3" json:"max_frame_size,omitempty"`
	// (Optional) An access token created by the caller only intended for use in
	// ALTS connections. The access token that should be used to authenticate to
	// the peer. The access token MUST be strongly bound to the ALTS credentials
	// used to establish the connection that the token is sent over.
	AccessToken string `protobuf:"bytes,11,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	// (Optional) Ordered transport protocol preferences supported by the client.
	TransportProtocolPreferences *TransportProtocolPreferences `protobuf:"bytes,12,opt,name=transport_protocol_preferences,json=transportProtocolPreferences,proto3" json:"transport_protocol_preferences,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *StartClientHandshakeReq) Reset() {
	*x = StartClientHandshakeReq{}
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartClientHandshakeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartClientHandshakeReq) ProtoMessage() {}

func (x *StartClientHandshakeReq) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartClientHandshakeReq.ProtoReflect.Descriptor instead.
func (*StartClientHandshakeReq) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_handshaker_proto_rawDescGZIP(), []int{2}
}

func (x *StartClientHandshakeReq) GetHandshakeSecurityProtocol() HandshakeProtocol {
	if x != nil {
		return x.HandshakeSecurityProtocol
	}
	return HandshakeProtocol_HANDSHAKE_PROTOCOL_UNSPECIFIED
}

func (x *StartClientHandshakeReq) GetApplicationProtocols() []string {
	if x != nil {
		return x.ApplicationProtocols
	}
	return nil
}

func (x *StartClientHandshakeReq) GetRecordProtocols() []string {
	if x != nil {
		return x.RecordProtocols
	}
	return nil
}

func (x *StartClientHandshakeReq) GetTargetIdentities() []*Identity {
	if x != nil {
		return x.TargetIdentities
	}
	return nil
}

func (x *StartClientHandshakeReq) GetLocalIdentity() *Identity {
	if x != nil {
		return x.LocalIdentity
	}
	return nil
}

func (x *StartClientHandshakeReq) GetLocalEndpoint() *Endpoint {
	if x != nil {
		return x.LocalEndpoint
	}
	return nil
}

func (x *StartClientHandshakeReq) GetRemoteEndpoint() *Endpoint {
	if x != nil {
		return x.RemoteEndpoint
	}
	return nil
}

func (x *StartClientHandshakeReq) GetTargetName() string {
	if x != nil {
		return x.TargetName
	}
	return ""
}

func (x *StartClientHandshakeReq) GetRpcVersions() *RpcProtocolVersions {
	if x != nil {
		return x.RpcVersions
	}
	return nil
}

func (x *StartClientHandshakeReq) GetMaxFrameSize() uint32 {
	if x != nil {
		return x.MaxFrameSize
	}
	return 0
}

func (x *StartClientHandshakeReq) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *StartClientHandshakeReq) GetTransportProtocolPreferences() *TransportProtocolPreferences {
	if x != nil {
		return x.TransportProtocolPreferences
	}
	return nil
}

type ServerHandshakeParameters struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The record protocols supported by the server, e.g.,
	// "ALTSRP_GCM_AES128".
	RecordProtocols []string `protobuf:"bytes,1,rep,name=record_protocols,json=recordProtocols,proto3" json:"record_protocols,omitempty"`
	// (Optional) A list of local identities supported by the server, if
	// specified. Otherwise, the handshaker chooses a default local identity.
	LocalIdentities []*Identity `protobuf:"bytes,2,rep,name=local_identities,json=localIdentities,proto3" json:"local_identities,omitempty"`
	// A token created by the caller only intended for use in
	// ALTS connections. The token should be used to authenticate to
	// the peer. The token MUST be strongly bound to the ALTS credentials
	// used to establish the connection that the token is sent over.
	Token         *string `protobuf:"bytes,3,opt,name=token,proto3,oneof" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerHandshakeParameters) Reset() {
	*x = ServerHandshakeParameters{}
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerHandshakeParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerHandshakeParameters) ProtoMessage() {}

func (x *ServerHandshakeParameters) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerHandshakeParameters.ProtoReflect.Descriptor instead.
func (*ServerHandshakeParameters) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_handshaker_proto_rawDescGZIP(), []int{3}
}

func (x *ServerHandshakeParameters) GetRecordProtocols() []string {
	if x != nil {
		return x.RecordProtocols
	}
	return nil
}

func (x *ServerHandshakeParameters) GetLocalIdentities() []*Identity {
	if x != nil {
		return x.LocalIdentities
	}
	return nil
}

func (x *ServerHandshakeParameters) GetToken() string {
	if x != nil && x.Token != nil {
		return *x.Token
	}
	return ""
}

type StartServerHandshakeReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The application protocols supported by the server, e.g., "h2" (for http2),
	// "grpc".
	ApplicationProtocols []string `protobuf:"bytes,1,rep,name=application_protocols,json=applicationProtocols,proto3" json:"application_protocols,omitempty"`
	// Handshake parameters (record protocols and local identities supported by
	// the server) mapped by the handshake protocol. Each handshake security
	// protocol (e.g., TLS or ALTS) has its own set of record protocols and local
	// identities. Since protobuf does not support enum as key to the map, the key
	// to handshake_parameters is the integer value of HandshakeProtocol enum.
	HandshakeParameters map[int32]*ServerHandshakeParameters `protobuf:"bytes,2,rep,name=handshake_parameters,json=handshakeParameters,proto3" json:"handshake_parameters,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Bytes in out_frames returned from the peer's HandshakerResp. It is possible
	// that the peer's out_frames are split into multiple HandshakeReq messages.
	InBytes []byte `protobuf:"bytes,3,opt,name=in_bytes,json=inBytes,proto3" json:"in_bytes,omitempty"`
	// (Optional) Local endpoint information of the connection to the client,
	// such as local IP address, port number, and network protocol.
	LocalEndpoint *Endpoint `protobuf:"bytes,4,opt,name=local_endpoint,json=localEndpoint,proto3" json:"local_endpoint,omitempty"`
	// (Optional) Endpoint information of the remote client, such as IP address,
	// port number, and network protocol.
	RemoteEndpoint *Endpoint `protobuf:"bytes,5,opt,name=remote_endpoint,json=remoteEndpoint,proto3" json:"remote_endpoint,omitempty"`
	// (Optional) RPC protocol versions supported by the server.
	RpcVersions *RpcProtocolVersions `protobuf:"bytes,6,opt,name=rpc_versions,json=rpcVersions,proto3" json:"rpc_versions,omitempty"`
	// (Optional) Maximum frame size supported by the server.
	MaxFrameSize uint32 `protobuf:"varint,7,opt,name=max_frame_size,json=maxFrameSize,proto3" json:"max_frame_size,omitempty"`
	// (Optional) Transport protocol preferences supported by the server.
	TransportProtocolPreferences *TransportProtocolPreferences `protobuf:"bytes,8,opt,name=transport_protocol_preferences,json=transportProtocolPreferences,proto3" json:"transport_protocol_preferences,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *StartServerHandshakeReq) Reset() {
	*x = StartServerHandshakeReq{}
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartServerHandshakeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartServerHandshakeReq) ProtoMessage() {}

func (x *StartServerHandshakeReq) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartServerHandshakeReq.ProtoReflect.Descriptor instead.
func (*StartServerHandshakeReq) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_handshaker_proto_rawDescGZIP(), []int{4}
}

func (x *StartServerHandshakeReq) GetApplicationProtocols() []string {
	if x != nil {
		return x.ApplicationProtocols
	}
	return nil
}

func (x *StartServerHandshakeReq) GetHandshakeParameters() map[int32]*ServerHandshakeParameters {
	if x != nil {
		return x.HandshakeParameters
	}
	return nil
}

func (x *StartServerHandshakeReq) GetInBytes() []byte {
	if x != nil {
		return x.InBytes
	}
	return nil
}

func (x *StartServerHandshakeReq) GetLocalEndpoint() *Endpoint {
	if x != nil {
		return x.LocalEndpoint
	}
	return nil
}

func (x *StartServerHandshakeReq) GetRemoteEndpoint() *Endpoint {
	if x != nil {
		return x.RemoteEndpoint
	}
	return nil
}

func (x *StartServerHandshakeReq) GetRpcVersions() *RpcProtocolVersions {
	if x != nil {
		return x.RpcVersions
	}
	return nil
}

func (x *StartServerHandshakeReq) GetMaxFrameSize() uint32 {
	if x != nil {
		return x.MaxFrameSize
	}
	return 0
}

func (x *StartServerHandshakeReq) GetTransportProtocolPreferences() *TransportProtocolPreferences {
	if x != nil {
		return x.TransportProtocolPreferences
	}
	return nil
}

type NextHandshakeMessageReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Bytes in out_frames returned from the peer's HandshakerResp. It is possible
	// that the peer's out_frames are split into multiple NextHandshakerMessageReq
	// messages.
	InBytes []byte `protobuf:"bytes,1,opt,name=in_bytes,json=inBytes,proto3" json:"in_bytes,omitempty"`
	// Number of milliseconds between when the application send the last handshake
	// message to the peer and when the application received the current handshake
	// message (in the in_bytes field) from the peer.
	NetworkLatencyMs uint32 `protobuf:"varint,2,opt,name=network_latency_ms,json=networkLatencyMs,proto3" json:"network_latency_ms,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *NextHandshakeMessageReq) Reset() {
	*x = NextHandshakeMessageReq{}
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NextHandshakeMessageReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NextHandshakeMessageReq) ProtoMessage() {}

func (x *NextHandshakeMessageReq) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NextHandshakeMessageReq.ProtoReflect.Descriptor instead.
func (*NextHandshakeMessageReq) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_handshaker_proto_rawDescGZIP(), []int{5}
}

func (x *NextHandshakeMessageReq) GetInBytes() []byte {
	if x != nil {
		return x.InBytes
	}
	return nil
}

func (x *NextHandshakeMessageReq) GetNetworkLatencyMs() uint32 {
	if x != nil {
		return x.NetworkLatencyMs
	}
	return 0
}

type HandshakerReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to ReqOneof:
	//
	//	*HandshakerReq_ClientStart
	//	*HandshakerReq_ServerStart
	//	*HandshakerReq_Next
	ReqOneof      isHandshakerReq_ReqOneof `protobuf_oneof:"req_oneof"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandshakerReq) Reset() {
	*x = HandshakerReq{}
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandshakerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandshakerReq) ProtoMessage() {}

func (x *HandshakerReq) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandshakerReq.ProtoReflect.Descriptor instead.
func (*HandshakerReq) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_handshaker_proto_rawDescGZIP(), []int{6}
}

func (x *HandshakerReq) GetReqOneof() isHandshakerReq_ReqOneof {
	if x != nil {
		return x.ReqOneof
	}
	return nil
}

func (x *HandshakerReq) GetClientStart() *StartClientHandshakeReq {
	if x != nil {
		if x, ok := x.ReqOneof.(*HandshakerReq_ClientStart); ok {
			return x.ClientStart
		}
	}
	return nil
}

func (x *HandshakerReq) GetServerStart() *StartServerHandshakeReq {
	if x != nil {
		if x, ok := x.ReqOneof.(*HandshakerReq_ServerStart); ok {
			return x.ServerStart
		}
	}
	return nil
}

func (x *HandshakerReq) GetNext() *NextHandshakeMessageReq {
	if x != nil {
		if x, ok := x.ReqOneof.(*HandshakerReq_Next); ok {
			return x.Next
		}
	}
	return nil
}

type isHandshakerReq_ReqOneof interface {
	isHandshakerReq_ReqOneof()
}

type HandshakerReq_ClientStart struct {
	// The start client handshake request message.
	ClientStart *StartClientHandshakeReq `protobuf:"bytes,1,opt,name=client_start,json=clientStart,proto3,oneof"`
}

type HandshakerReq_ServerStart struct {
	// The start server handshake request message.
	ServerStart *StartServerHandshakeReq `protobuf:"bytes,2,opt,name=server_start,json=serverStart,proto3,oneof"`
}

type HandshakerReq_Next struct {
	// The next handshake request message.
	Next *NextHandshakeMessageReq `protobuf:"bytes,3,opt,name=next,proto3,oneof"`
}

func (*HandshakerReq_ClientStart) isHandshakerReq_ReqOneof() {}

func (*HandshakerReq_ServerStart) isHandshakerReq_ReqOneof() {}

func (*HandshakerReq_Next) isHandshakerReq_ReqOneof() {}

type HandshakerResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The application protocol negotiated for this connection.
	ApplicationProtocol string `protobuf:"bytes,1,opt,name=application_protocol,json=applicationProtocol,proto3" json:"application_protocol,omitempty"`
	// The record protocol negotiated for this connection.
	RecordProtocol string `protobuf:"bytes,2,opt,name=record_protocol,json=recordProtocol,proto3" json:"record_protocol,omitempty"`
	// Cryptographic key data. The key data may be more than the key length
	// required for the record protocol, thus the client of the handshaker
	// service needs to truncate the key data into the right key length.
	KeyData []byte `protobuf:"bytes,3,opt,name=key_data,json=keyData,proto3" json:"key_data,omitempty"`
	// The authenticated identity of the peer.
	PeerIdentity *Identity `protobuf:"bytes,4,opt,name=peer_identity,json=peerIdentity,proto3" json:"peer_identity,omitempty"`
	// The local identity used in the handshake.
	LocalIdentity *Identity `protobuf:"bytes,5,opt,name=local_identity,json=localIdentity,proto3" json:"local_identity,omitempty"`
	// Indicate whether the handshaker service client should keep the channel
	// between the handshaker service open, e.g., in order to handle
	// post-handshake messages in the future.
	KeepChannelOpen bool `protobuf:"varint,6,opt,name=keep_channel_open,json=keepChannelOpen,proto3" json:"keep_channel_open,omitempty"`
	// The RPC protocol versions supported by the peer.
	PeerRpcVersions *RpcProtocolVersions `protobuf:"bytes,7,opt,name=peer_rpc_versions,json=peerRpcVersions,proto3" json:"peer_rpc_versions,omitempty"`
	// The maximum frame size of the peer.
	MaxFrameSize uint32 `protobuf:"varint,8,opt,name=max_frame_size,json=maxFrameSize,proto3" json:"max_frame_size,omitempty"`
	// (Optional) The transport protocol negotiated for this connection.
	TransportProtocol *NegotiatedTransportProtocol `protobuf:"bytes,9,opt,name=transport_protocol,json=transportProtocol,proto3" json:"transport_protocol,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *HandshakerResult) Reset() {
	*x = HandshakerResult{}
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandshakerResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandshakerResult) ProtoMessage() {}

func (x *HandshakerResult) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandshakerResult.ProtoReflect.Descriptor instead.
func (*HandshakerResult) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_handshaker_proto_rawDescGZIP(), []int{7}
}

func (x *HandshakerResult) GetApplicationProtocol() string {
	if x != nil {
		return x.ApplicationProtocol
	}
	return ""
}

func (x *HandshakerResult) GetRecordProtocol() string {
	if x != nil {
		return x.RecordProtocol
	}
	return ""
}

func (x *HandshakerResult) GetKeyData() []byte {
	if x != nil {
		return x.KeyData
	}
	return nil
}

func (x *HandshakerResult) GetPeerIdentity() *Identity {
	if x != nil {
		return x.PeerIdentity
	}
	return nil
}

func (x *HandshakerResult) GetLocalIdentity() *Identity {
	if x != nil {
		return x.LocalIdentity
	}
	return nil
}

func (x *HandshakerResult) GetKeepChannelOpen() bool {
	if x != nil {
		return x.KeepChannelOpen
	}
	return false
}

func (x *HandshakerResult) GetPeerRpcVersions() *RpcProtocolVersions {
	if x != nil {
		return x.PeerRpcVersions
	}
	return nil
}

func (x *HandshakerResult) GetMaxFrameSize() uint32 {
	if x != nil {
		return x.MaxFrameSize
	}
	return 0
}

func (x *HandshakerResult) GetTransportProtocol() *NegotiatedTransportProtocol {
	if x != nil {
		return x.TransportProtocol
	}
	return nil
}

type HandshakerStatus struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The status code. This could be the gRPC status code.
	Code uint32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	// The status details.
	Details       string `protobuf:"bytes,2,opt,name=details,proto3" json:"details,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandshakerStatus) Reset() {
	*x = HandshakerStatus{}
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandshakerStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandshakerStatus) ProtoMessage() {}

func (x *HandshakerStatus) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandshakerStatus.ProtoReflect.Descriptor instead.
func (*HandshakerStatus) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_handshaker_proto_rawDescGZIP(), []int{8}
}

func (x *HandshakerStatus) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *HandshakerStatus) GetDetails() string {
	if x != nil {
		return x.Details
	}
	return ""
}

type HandshakerResp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Frames to be given to the peer for the NextHandshakeMessageReq. May be
	// empty if no out_frames have to be sent to the peer or if in_bytes in the
	// HandshakerReq are incomplete. All the non-empty out frames must be sent to
	// the peer even if the handshaker status is not OK as these frames may
	// contain the alert frames.
	OutFrames []byte `protobuf:"bytes,1,opt,name=out_frames,json=outFrames,proto3" json:"out_frames,omitempty"`
	// Number of bytes in the in_bytes consumed by the handshaker. It is possible
	// that part of in_bytes in HandshakerReq was unrelated to the handshake
	// process.
	BytesConsumed uint32 `protobuf:"varint,2,opt,name=bytes_consumed,json=bytesConsumed,proto3" json:"bytes_consumed,omitempty"`
	// This is set iff the handshake was successful. out_frames may still be set
	// to frames that needs to be forwarded to the peer.
	Result *HandshakerResult `protobuf:"bytes,3,opt,name=result,proto3" json:"result,omitempty"`
	// Status of the handshaker.
	Status        *HandshakerStatus `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandshakerResp) Reset() {
	*x = HandshakerResp{}
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandshakerResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandshakerResp) ProtoMessage() {}

func (x *HandshakerResp) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_handshaker_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandshakerResp.ProtoReflect.Descriptor instead.
func (*HandshakerResp) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_handshaker_proto_rawDescGZIP(), []int{9}
}

func (x *HandshakerResp) GetOutFrames() []byte {
	if x != nil {
		return x.OutFrames
	}
	return nil
}

func (x *HandshakerResp) GetBytesConsumed() uint32 {
	if x != nil {
		return x.BytesConsumed
	}
	return 0
}

func (x *HandshakerResp) GetResult() *HandshakerResult {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *HandshakerResp) GetStatus() *HandshakerStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_grpc_gcp_handshaker_proto protoreflect.FileDescriptor

const file_grpc_gcp_handshaker_proto_rawDesc = "" +
	"\n" +
	"\x19grpc/gcp/handshaker.proto\x12\bgrpc.gcp\x1a(grpc/gcp/transport_security_common.proto\"t\n" +
	"\bEndpoint\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x01 \x01(\tR\tipAddress\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x05R\x04port\x125\n" +
	"\bprotocol\x18\x03 \x01(\x0e2\x19.grpc.gcp.NetworkProtocolR\bprotocol\"\xe8\x01\n" +
	"\bIdentity\x12)\n" +
	"\x0fservice_account\x18\x01 \x01(\tH\x00R\x0eserviceAccount\x12\x1c\n" +
	"\bhostname\x18\x02 \x01(\tH\x00R\bhostname\x12B\n" +
	"\n" +
	"attributes\x18\x03 \x03(\v2\".grpc.gcp.Identity.AttributesEntryR\n" +
	"attributes\x1a=\n" +
	"\x0fAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\x10\n" +
	"\x0eidentity_oneof\"\xe9\x05\n" +
	"\x17StartClientHandshakeReq\x12[\n" +
	"\x1bhandshake_security_protocol\x18\x01 \x01(\x0e2\x1b.grpc.gcp.HandshakeProtocolR\x19handshakeSecurityProtocol\x123\n" +
	"\x15application_protocols\x18\x02 \x03(\tR\x14applicationProtocols\x12)\n" +
	"\x10record_protocols\x18\x03 \x03(\tR\x0frecordProtocols\x12?\n" +
	"\x11target_identities\x18\x04 \x03(\v2\x12.grpc.gcp.IdentityR\x10targetIdentities\x129\n" +
	"\x0elocal_identity\x18\x05 \x01(\v2\x12.grpc.gcp.IdentityR\rlocalIdentity\x129\n" +
	"\x0elocal_endpoint\x18\x06 \x01(\v2\x12.grpc.gcp.EndpointR\rlocalEndpoint\x12;\n" +
	"\x0fremote_endpoint\x18\a \x01(\v2\x12.grpc.gcp.EndpointR\x0eremoteEndpoint\x12\x1f\n" +
	"\vtarget_name\x18\b \x01(\tR\n" +
	"targetName\x12@\n" +
	"\frpc_versions\x18\t \x01(\v2\x1d.grpc.gcp.RpcProtocolVersionsR\vrpcVersions\x12$\n" +
	"\x0emax_frame_size\x18\n" +
	" \x01(\rR\fmaxFrameSize\x12&\n" +
	"\faccess_token\x18\v \x01(\tB\x03\x80\x01\x01R\vaccessToken\x12l\n" +
	"\x1etransport_protocol_preferences\x18\f \x01(\v2&.grpc.gcp.TransportProtocolPreferencesR\x1ctransportProtocolPreferences\"\xaf\x01\n" +
	"\x19ServerHandshakeParameters\x12)\n" +
	"\x10record_protocols\x18\x01 \x03(\tR\x0frecordProtocols\x12=\n" +
	"\x10local_identities\x18\x02 \x03(\v2\x12.grpc.gcp.IdentityR\x0flocalIdentities\x12\x1e\n" +
	"\x05token\x18\x03 \x01(\tB\x03\x80\x01\x01H\x00R\x05token\x88\x01\x01B\b\n" +
	"\x06_token\"\x93\x05\n" +
	"\x17StartServerHandshakeReq\x123\n" +
	"\x15application_protocols\x18\x01 \x03(\tR\x14applicationProtocols\x12m\n" +
	"\x14handshake_parameters\x18\x02 \x03(\v2:.grpc.gcp.StartServerHandshakeReq.HandshakeParametersEntryR\x13handshakeParameters\x12\x19\n" +
	"\bin_bytes\x18\x03 \x01(\fR\ainBytes\x129\n" +
	"\x0elocal_endpoint\x18\x04 \x01(\v2\x12.grpc.gcp.EndpointR\rlocalEndpoint\x12;\n" +
	"\x0fremote_endpoint\x18\x05 \x01(\v2\x12.grpc.gcp.EndpointR\x0eremoteEndpoint\x12@\n" +
	"\frpc_versions\x18\x06 \x01(\v2\x1d.grpc.gcp.RpcProtocolVersionsR\vrpcVersions\x12$\n" +
	"\x0emax_frame_size\x18\a \x01(\rR\fmaxFrameSize\x12l\n" +
	"\x1etransport_protocol_preferences\x18\b \x01(\v2&.grpc.gcp.TransportProtocolPreferencesR\x1ctransportProtocolPreferences\x1ak\n" +
	"\x18HandshakeParametersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x129\n" +
	"\x05value\x18\x02 \x01(\v2#.grpc.gcp.ServerHandshakeParametersR\x05value:\x028\x01\"b\n" +
	"\x17NextHandshakeMessageReq\x12\x19\n" +
	"\bin_bytes\x18\x01 \x01(\fR\ainBytes\x12,\n" +
	"\x12network_latency_ms\x18\x02 \x01(\rR\x10networkLatencyMs\"\xe5\x01\n" +
	"\rHandshakerReq\x12F\n" +
	"\fclient_start\x18\x01 \x01(\v2!.grpc.gcp.StartClientHandshakeReqH\x00R\vclientStart\x12F\n" +
	"\fserver_start\x18\x02 \x01(\v2!.grpc.gcp.StartServerHandshakeReqH\x00R\vserverStart\x127\n" +
	"\x04next\x18\x03 \x01(\v2!.grpc.gcp.NextHandshakeMessageReqH\x00R\x04nextB\v\n" +
	"\treq_oneof\"\xf0\x03\n" +
	"\x10HandshakerResult\x121\n" +
	"\x14application_protocol\x18\x01 \x01(\tR\x13applicationProtocol\x12'\n" +
	"\x0frecord_protocol\x18\x02 \x01(\tR\x0erecordProtocol\x12\x19\n" +
	"\bkey_data\x18\x03 \x01(\fR\akeyData\x127\n" +
	"\rpeer_identity\x18\x04 \x01(\v2\x12.grpc.gcp.IdentityR\fpeerIdentity\x129\n" +
	"\x0elocal_identity\x18\x05 \x01(\v2\x12.grpc.gcp.IdentityR\rlocalIdentity\x12*\n" +
	"\x11keep_channel_open\x18\x06 \x01(\bR\x0fkeepChannelOpen\x12I\n" +
	"\x11peer_rpc_versions\x18\a \x01(\v2\x1d.grpc.gcp.RpcProtocolVersionsR\x0fpeerRpcVersions\x12$\n" +
	"\x0emax_frame_size\x18\b \x01(\rR\fmaxFrameSize\x12T\n" +
	"\x12transport_protocol\x18\t \x01(\v2%.grpc.gcp.NegotiatedTransportProtocolR\x11transportProtocol\"@\n" +
	"\x10HandshakerStatus\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\adetails\x18\x02 \x01(\tR\adetails\"\xbe\x01\n" +
	"\x0eHandshakerResp\x12\x1d\n" +
	"\n" +
	"out_frames\x18\x01 \x01(\fR\toutFrames\x12%\n" +
	"\x0ebytes_consumed\x18\x02 \x01(\rR\rbytesConsumed\x122\n" +
	"\x06result\x18\x03 \x01(\v2\x1a.grpc.gcp.HandshakerResultR\x06result\x122\n" +
	"\x06status\x18\x04 \x01(\v2\x1a.grpc.gcp.HandshakerStatusR\x06status*J\n" +
	"\x11HandshakeProtocol\x12\"\n" +
	"\x1eHANDSHAKE_PROTOCOL_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03TLS\x10\x01\x12\b\n" +
	"\x04ALTS\x10\x02*E\n" +
	"\x0fNetworkProtocol\x12 \n" +
	"\x1cNETWORK_PROTOCOL_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03TCP\x10\x01\x12\a\n" +
	"\x03UDP\x10\x022[\n" +
	"\x11HandshakerService\x12F\n" +
	"\vDoHandshake\x12\x17.grpc.gcp.HandshakerReq\x1a\x18.grpc.gcp.HandshakerResp\"\x00(\x010\x01Bk\n" +
	"\x15io.grpc.alts.internalB\x0fHandshakerProtoP\x01Z?google.golang.org/grpc/credentials/alts/internal/proto/grpc_gcpb\x06proto3"

var (
	file_grpc_gcp_handshaker_proto_rawDescOnce sync.Once
	file_grpc_gcp_handshaker_proto_rawDescData []byte
)

func file_grpc_gcp_handshaker_proto_rawDescGZIP() []byte {
	file_grpc_gcp_handshaker_proto_rawDescOnce.Do(func() {
		file_grpc_gcp_handshaker_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_grpc_gcp_handshaker_proto_rawDesc), len(file_grpc_gcp_handshaker_proto_rawDesc)))
	})
	return file_grpc_gcp_handshaker_proto_rawDescData
}

var file_grpc_gcp_handshaker_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_grpc_gcp_handshaker_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_grpc_gcp_handshaker_proto_goTypes = []any{
	(HandshakeProtocol)(0),               // 0: grpc.gcp.HandshakeProtocol
	(NetworkProtocol)(0),                 // 1: grpc.gcp.NetworkProtocol
	(*Endpoint)(nil),                     // 2: grpc.gcp.Endpoint
	(*Identity)(nil),                     // 3: grpc.gcp.Identity
	(*StartClientHandshakeReq)(nil),      // 4: grpc.gcp.StartClientHandshakeReq
	(*ServerHandshakeParameters)(nil),    // 5: grpc.gcp.ServerHandshakeParameters
	(*StartServerHandshakeReq)(nil),      // 6: grpc.gcp.StartServerHandshakeReq
	(*NextHandshakeMessageReq)(nil),      // 7: grpc.gcp.NextHandshakeMessageReq
	(*HandshakerReq)(nil),                // 8: grpc.gcp.HandshakerReq
	(*HandshakerResult)(nil),             // 9: grpc.gcp.HandshakerResult
	(*HandshakerStatus)(nil),             // 10: grpc.gcp.HandshakerStatus
	(*HandshakerResp)(nil),               // 11: grpc.gcp.HandshakerResp
	nil,                                  // 12: grpc.gcp.Identity.AttributesEntry
	nil,                                  // 13: grpc.gcp.StartServerHandshakeReq.HandshakeParametersEntry
	(*RpcProtocolVersions)(nil),          // 14: grpc.gcp.RpcProtocolVersions
	(*TransportProtocolPreferences)(nil), // 15: grpc.gcp.TransportProtocolPreferences
	(*NegotiatedTransportProtocol)(nil),  // 16: grpc.gcp.NegotiatedTransportProtocol
}
var file_grpc_gcp_handshaker_proto_depIdxs = []int32{
	1,  // 0: grpc.gcp.Endpoint.protocol:type_name -> grpc.gcp.NetworkProtocol
	12, // 1: grpc.gcp.Identity.attributes:type_name -> grpc.gcp.Identity.AttributesEntry
	0,  // 2: grpc.gcp.StartClientHandshakeReq.handshake_security_protocol:type_name -> grpc.gcp.HandshakeProtocol
	3,  // 3: grpc.gcp.StartClientHandshakeReq.target_identities:type_name -> grpc.gcp.Identity
	3,  // 4: grpc.gcp.StartClientHandshakeReq.local_identity:type_name -> grpc.gcp.Identity
	2,  // 5: grpc.gcp.StartClientHandshakeReq.local_endpoint:type_name -> grpc.gcp.Endpoint
	2,  // 6: grpc.gcp.StartClientHandshakeReq.remote_endpoint:type_name -> grpc.gcp.Endpoint
	14, // 7: grpc.gcp.StartClientHandshakeReq.rpc_versions:type_name -> grpc.gcp.RpcProtocolVersions
	15, // 8: grpc.gcp.StartClientHandshakeReq.transport_protocol_preferences:type_name -> grpc.gcp.TransportProtocolPreferences
	3,  // 9: grpc.gcp.ServerHandshakeParameters.local_identities:type_name -> grpc.gcp.Identity
	13, // 10: grpc.gcp.StartServerHandshakeReq.handshake_parameters:type_name -> grpc.gcp.StartServerHandshakeReq.HandshakeParametersEntry
	2,  // 11: grpc.gcp.StartServerHandshakeReq.local_endpoint:type_name -> grpc.gcp.Endpoint
	2,  // 12: grpc.gcp.StartServerHandshakeReq.remote_endpoint:type_name -> grpc.gcp.Endpoint
	14, // 13: grpc.gcp.StartServerHandshakeReq.rpc_versions:type_name -> grpc.gcp.RpcProtocolVersions
	15, // 14: grpc.gcp.StartServerHandshakeReq.transport_protocol_preferences:type_name -> grpc.gcp.TransportProtocolPreferences
	4,  // 15: grpc.gcp.HandshakerReq.client_start:type_name -> grpc.gcp.StartClientHandshakeReq
	6,  // 16: grpc.gcp.HandshakerReq.server_start:type_name -> grpc.gcp.StartServerHandshakeReq
	7,  // 17: grpc.gcp.HandshakerReq.next:type_name -> grpc.gcp.NextHandshakeMessageReq
	3,  // 18: grpc.gcp.HandshakerResult.peer_identity:type_name -> grpc.gcp.Identity
	3,  // 19: grpc.gcp.HandshakerResult.local_identity:type_name -> grpc.gcp.Identity
	14, // 20: grpc.gcp.HandshakerResult.peer_rpc_versions:type_name -> grpc.gcp.RpcProtocolVersions
	16, // 21: grpc.gcp.HandshakerResult.transport_protocol:type_name -> grpc.gcp.NegotiatedTransportProtocol
	9,  // 22: grpc.gcp.HandshakerResp.result:type_name -> grpc.gcp.HandshakerResult
	10, // 23: grpc.gcp.HandshakerResp.status:type_name -> grpc.gcp.HandshakerStatus
	5,  // 24: grpc.gcp.StartServerHandshakeReq.HandshakeParametersEntry.value:type_name -> grpc.gcp.ServerHandshakeParameters
	8,  // 25: grpc.gcp.HandshakerService.DoHandshake:input_type -> grpc.gcp.HandshakerReq
	11, // 26: grpc.gcp.HandshakerService.DoHandshake:output_type -> grpc.gcp.HandshakerResp
	26, // [26:27] is the sub-list for method output_type
	25, // [25:26] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_grpc_gcp_handshaker_proto_init() }
func file_grpc_gcp_handshaker_proto_init() {
	if File_grpc_gcp_handshaker_proto != nil {
		return
	}
	file_grpc_gcp_transport_security_common_proto_init()
	file_grpc_gcp_handshaker_proto_msgTypes[1].OneofWrappers = []any{
		(*Identity_ServiceAccount)(nil),
		(*Identity_Hostname)(nil),
	}
	file_grpc_gcp_handshaker_proto_msgTypes[3].OneofWrappers = []any{}
	file_grpc_gcp_handshaker_proto_msgTypes[6].OneofWrappers = []any{
		(*HandshakerReq_ClientStart)(nil),
		(*HandshakerReq_ServerStart)(nil),
		(*HandshakerReq_Next)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_grpc_gcp_handshaker_proto_rawDesc), len(file_grpc_gcp_handshaker_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_grpc_gcp_handshaker_proto_goTypes,
		DependencyIndexes: file_grpc_gcp_handshaker_proto_depIdxs,
		EnumInfos:         file_grpc_gcp_handshaker_proto_enumTypes,
		MessageInfos:      file_grpc_gcp_handshaker_proto_msgTypes,
	}.Build()
	File_grpc_gcp_handshaker_proto = out.File
	file_grpc_gcp_handshaker_proto_goTypes = nil
	file_grpc_gcp_handshaker_proto_depIdxs = nil
}
