/*
 *
 * Copyright 2018 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package authinfo

import (
	"reflect"
	"testing"

	altspb "google.golang.org/grpc/credentials/alts/internal/proto/grpc_gcp"
	"google.golang.org/grpc/internal/grpctest"
)

type s struct {
	grpctest.Tester
}

func Test(t *testing.T) {
	grpctest.RunSubTests(t, s{})
}

const (
	testAppProtocol             = "my_app"
	testRecordProtocol          = "very_secure_protocol"
	testPeerAccount             = "peer_service_account"
	testLocalAccount            = "local_service_account"
	testPeerHostname            = "peer_hostname"
	testLocalHostname           = "local_hostname"
	testLocalPeerAttributeKey   = "peer"
	testLocalPeerAttributeValue = "attributes"
)

func (s) TestALTSAuthInfo(t *testing.T) {
	testPeerAttributes := make(map[string]string)
	testPeerAttributes[testLocalPeerAttributeKey] = testLocalPeerAttributeValue
	for _, tc := range []struct {
		result             *altspb.HandshakerResult
		outAppProtocol     string
		outRecordProtocol  string
		outSecurityLevel   altspb.SecurityLevel
		outPeerAccount     string
		outLocalAccount    string
		outPeerRPCVersions *altspb.RpcProtocolVersions
		outPeerAttributes  map[string]string
	}{
		{
			&altspb.HandshakerResult{
				ApplicationProtocol: testAppProtocol,
				RecordProtocol:      testRecordProtocol,
				PeerIdentity: &altspb.Identity{
					IdentityOneof: &altspb.Identity_ServiceAccount{
						ServiceAccount: testPeerAccount,
					},
					Attributes: testPeerAttributes,
				},
				LocalIdentity: &altspb.Identity{
					IdentityOneof: &altspb.Identity_ServiceAccount{
						ServiceAccount: testLocalAccount,
					},
				},
			},
			testAppProtocol,
			testRecordProtocol,
			altspb.SecurityLevel_INTEGRITY_AND_PRIVACY,
			testPeerAccount,
			testLocalAccount,
			nil,
			testPeerAttributes,
		},
		{
			&altspb.HandshakerResult{
				ApplicationProtocol: testAppProtocol,
				RecordProtocol:      testRecordProtocol,
				PeerIdentity: &altspb.Identity{
					IdentityOneof: &altspb.Identity_Hostname{
						Hostname: testPeerHostname,
					},
					Attributes: testPeerAttributes,
				},
				LocalIdentity: &altspb.Identity{
					IdentityOneof: &altspb.Identity_Hostname{
						Hostname: testLocalHostname,
					},
				},
				PeerRpcVersions: &altspb.RpcProtocolVersions{
					MaxRpcVersion: &altspb.RpcProtocolVersions_Version{
						Major: 20,
						Minor: 21,
					},
					MinRpcVersion: &altspb.RpcProtocolVersions_Version{
						Major: 10,
						Minor: 11,
					},
				},
			},
			testAppProtocol,
			testRecordProtocol,
			altspb.SecurityLevel_INTEGRITY_AND_PRIVACY,
			"",
			"",
			&altspb.RpcProtocolVersions{
				MaxRpcVersion: &altspb.RpcProtocolVersions_Version{
					Major: 20,
					Minor: 21,
				},
				MinRpcVersion: &altspb.RpcProtocolVersions_Version{
					Major: 10,
					Minor: 11,
				},
			},
			testPeerAttributes,
		},
	} {
		authInfo := newAuthInfo(tc.result)
		if got, want := authInfo.AuthType(), "alts"; got != want {
			t.Errorf("authInfo.AuthType()=%v, want %v", got, want)
		}
		if got, want := authInfo.ApplicationProtocol(), tc.outAppProtocol; got != want {
			t.Errorf("authInfo.ApplicationProtocol()=%v, want %v", got, want)
		}
		if got, want := authInfo.RecordProtocol(), tc.outRecordProtocol; got != want {
			t.Errorf("authInfo.RecordProtocol()=%v, want %v", got, want)
		}
		if got, want := authInfo.SecurityLevel(), tc.outSecurityLevel; got != want {
			t.Errorf("authInfo.SecurityLevel()=%v, want %v", got, want)
		}
		if got, want := authInfo.PeerServiceAccount(), tc.outPeerAccount; got != want {
			t.Errorf("authInfo.PeerServiceAccount()=%v, want %v", got, want)
		}
		if got, want := authInfo.LocalServiceAccount(), tc.outLocalAccount; got != want {
			t.Errorf("authInfo.LocalServiceAccount()=%v, want %v", got, want)
		}
		if got, want := authInfo.PeerRPCVersions(), tc.outPeerRPCVersions; !reflect.DeepEqual(got, want) {
			t.Errorf("authinfo.PeerRpcVersions()=%v, want %v", got, want)
		}
		if got, want := authInfo.PeerAttributes(), tc.outPeerAttributes; !reflect.DeepEqual(got, want) {
			t.Errorf("authinfo.PeerAttributes()=%v, want %v", got, want)
		}

	}
}
