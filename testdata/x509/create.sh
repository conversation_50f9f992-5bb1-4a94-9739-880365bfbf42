#!/bin/bash

# Create the server CA certs.
openssl req -x509                                     \
  -newkey rsa:4096                                    \
  -nodes                                              \
  -days 3650                                          \
  -keyout server_ca_key.pem                           \
  -out server_ca_cert.pem                             \
  -subj /C=US/ST=CA/L=SVL/O=gRPC/CN=test-server_ca/   \
  -config ./openssl.cnf                               \
  -extensions test_ca                                 \
  -sha256

# Create the client CA certs.
openssl req -x509                                     \
  -newkey rsa:4096                                    \
  -nodes                                              \
  -days 3650                                          \
  -keyout client_ca_key.pem                           \
  -out client_ca_cert.pem                             \
  -subj /C=US/ST=CA/L=SVL/O=gRPC/CN=test-client_ca/   \
  -config ./openssl.cnf                               \
  -extensions test_ca                                 \
  -sha256

# Generate two server certs.
openssl genrsa -out server1_key.pem 4096
openssl req -new                                    \
  -key server1_key.pem                              \
  -days 3650                                        \
  -out server1_csr.pem                              \
  -subj /C=US/ST=CA/L=SVL/O=gRPC/CN=test-server1/   \
  -config ./openssl.cnf                             \
  -reqexts test_server
openssl x509 -req           \
  -in server1_csr.pem       \
  -CAkey server_ca_key.pem  \
  -CA server_ca_cert.pem    \
  -days 3650                \
  -set_serial 1000          \
  -out server1_cert.pem     \
  -extfile ./openssl.cnf    \
  -extensions test_server   \
  -sha256
openssl verify -verbose -CAfile server_ca_cert.pem  server1_cert.pem

openssl genrsa -out server2_key.pem 4096
openssl req -new                                    \
  -key server2_key.pem                              \
  -days 3650                                        \
  -out server2_csr.pem                              \
  -subj /C=US/ST=CA/L=SVL/O=gRPC/CN=test-server2/   \
  -config ./openssl.cnf                             \
  -reqexts test_server
openssl x509 -req           \
  -in server2_csr.pem       \
  -CAkey server_ca_key.pem  \
  -CA server_ca_cert.pem    \
  -days 3650                \
  -set_serial 1000          \
  -out server2_cert.pem     \
  -extfile ./openssl.cnf    \
  -extensions test_server   \
  -sha256
openssl verify -verbose -CAfile server_ca_cert.pem  server2_cert.pem

# Generate two client certs.
openssl genrsa -out client1_key.pem 4096
openssl req -new                                    \
  -key client1_key.pem                              \
  -days 3650                                        \
  -out client1_csr.pem                              \
  -subj /C=US/ST=CA/L=SVL/O=gRPC/CN=test-client1/   \
  -config ./openssl.cnf                             \
  -reqexts test_client
openssl x509 -req           \
  -in client1_csr.pem       \
  -CAkey client_ca_key.pem  \
  -CA client_ca_cert.pem    \
  -days 3650                \
  -set_serial 1000          \
  -out client1_cert.pem     \
  -extfile ./openssl.cnf    \
  -extensions test_client   \
  -sha256
openssl verify -verbose -CAfile client_ca_cert.pem  client1_cert.pem

openssl genrsa -out client2_key.pem 4096
openssl req -new                                    \
  -key client2_key.pem                              \
  -days 3650                                        \
  -out client2_csr.pem                              \
  -subj /C=US/ST=CA/L=SVL/O=gRPC/CN=test-client2/   \
  -config ./openssl.cnf                             \
  -reqexts test_client
openssl x509 -req           \
  -in client2_csr.pem       \
  -CAkey client_ca_key.pem  \
  -CA client_ca_cert.pem    \
  -days 3650                \
  -set_serial 1000          \
  -out client2_cert.pem     \
  -extfile ./openssl.cnf    \
  -extensions test_client   \
  -sha256
openssl verify -verbose -CAfile client_ca_cert.pem  client2_cert.pem

# Generate a cert with SPIFFE ID.
openssl req -x509                                                         \
  -newkey rsa:4096                                                        \
  -keyout spiffe_key.pem                                                  \
  -out spiffe_cert.pem                                                    \
  -nodes                                                                  \
  -days 3650                                                              \
  -subj /C=US/ST=CA/L=SVL/O=gRPC/CN=test-client1/                         \
  -addext "subjectAltName = URI:spiffe://foo.bar.com/client/workload/1"   \
  -sha256

# Generate a cert with SPIFFE ID and another SAN URI field(which doesn't meet SPIFFE specs).
openssl req -x509                                                         \
  -newkey rsa:4096                                                        \
  -keyout multiple_uri_key.pem                                            \
  -out multiple_uri_cert.pem                                              \
  -nodes                                                                  \
  -days 3650                                                              \
  -subj /C=US/ST=CA/L=SVL/O=gRPC/CN=test-client1/                         \
  -addext "subjectAltName = URI:spiffe://foo.bar.com/client/workload/1, URI:https://bar.baz.com/client" \
  -sha256

# Generate a cert with SPIFFE ID using client_with_spiffe_openssl.cnf
openssl req -new                                    \
  -key client_with_spiffe_key.pem                   \
  -out client_with_spiffe_csr.pem                   \
  -subj /C=US/ST=CA/L=SVL/O=gRPC/CN=test-client1/   \
  -config ./client_with_spiffe_openssl.cnf          \
  -reqexts test_client
openssl x509 -req                              \
  -in client_with_spiffe_csr.pem               \
  -CAkey client_ca_key.pem                     \
  -CA client_ca_cert.pem                       \
  -days 3650                                   \
  -set_serial 1000                             \
  -out client_with_spiffe_cert.pem             \
  -extfile ./client_with_spiffe_openssl.cnf    \
  -extensions test_client                      \
  -sha256
openssl verify -verbose -CAfile client_with_spiffe_cert.pem

# Cleanup the CSRs.
rm *_csr.pem
