/*
 * Copyright 2022 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

// Code generated by protoc-gen-go.
// source: testv3.proto
// DO NOT EDIT!

/*
Package grpc_testing_not_regenerated is a generated protocol buffer package.

It is generated from these files:

	testv3.proto

It has these top-level messages:

	SearchResponseV3
	SearchRequestV3
*/
package grpc_testing_not_regenerated

import (
	context "context"
	fmt "fmt"
	math "math"

	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SearchResponseV3_State int32

const (
	SearchResponseV3_UNKNOWN SearchResponseV3_State = 0
	SearchResponseV3_FRESH   SearchResponseV3_State = 1
	SearchResponseV3_STALE   SearchResponseV3_State = 2
)

var SearchResponseV3_State_name = map[int32]string{
	0: "UNKNOWN",
	1: "FRESH",
	2: "STALE",
}
var SearchResponseV3_State_value = map[string]int32{
	"UNKNOWN": 0,
	"FRESH":   1,
	"STALE":   2,
}

func (x SearchResponseV3_State) String() string {
	return proto.EnumName(SearchResponseV3_State_name, int32(x))
}
func (SearchResponseV3_State) EnumDescriptor() ([]byte, []int) { return fileDescriptor0, []int{0, 0} }

type SearchResponseV3 struct {
	Results []*SearchResponseV3_Result `protobuf:"bytes,1,rep,name=results" json:"results,omitempty"`
	State   SearchResponseV3_State     `protobuf:"varint,2,opt,name=state,enum=grpc.testingv3.SearchResponseV3_State" json:"state,omitempty"`
}

func (m *SearchResponseV3) Reset()                    { *m = SearchResponseV3{} }
func (m *SearchResponseV3) String() string            { return proto.CompactTextString(m) }
func (*SearchResponseV3) ProtoMessage()               {}
func (*SearchResponseV3) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

func (m *SearchResponseV3) GetResults() []*SearchResponseV3_Result {
	if m != nil {
		return m.Results
	}
	return nil
}

func (m *SearchResponseV3) GetState() SearchResponseV3_State {
	if m != nil {
		return m.State
	}
	return SearchResponseV3_UNKNOWN
}

type SearchResponseV3_Result struct {
	Url      string                                    `protobuf:"bytes,1,opt,name=url" json:"url,omitempty"`
	Title    string                                    `protobuf:"bytes,2,opt,name=title" json:"title,omitempty"`
	Snippets []string                                  `protobuf:"bytes,3,rep,name=snippets" json:"snippets,omitempty"`
	Metadata map[string]*SearchResponseV3_Result_Value `protobuf:"bytes,4,rep,name=metadata" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
}

func (m *SearchResponseV3_Result) Reset()                    { *m = SearchResponseV3_Result{} }
func (m *SearchResponseV3_Result) String() string            { return proto.CompactTextString(m) }
func (*SearchResponseV3_Result) ProtoMessage()               {}
func (*SearchResponseV3_Result) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{0, 0} }

func (m *SearchResponseV3_Result) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *SearchResponseV3_Result) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SearchResponseV3_Result) GetSnippets() []string {
	if m != nil {
		return m.Snippets
	}
	return nil
}

func (m *SearchResponseV3_Result) GetMetadata() map[string]*SearchResponseV3_Result_Value {
	if m != nil {
		return m.Metadata
	}
	return nil
}

type SearchResponseV3_Result_Value struct {
	// Types that are valid to be assigned to Val:
	//	*SearchResponseV3_Result_Value_Str
	//	*SearchResponseV3_Result_Value_Int
	//	*SearchResponseV3_Result_Value_Real
	Val isSearchResponseV3_Result_Value_Val `protobuf_oneof:"val"`
}

func (m *SearchResponseV3_Result_Value) Reset()         { *m = SearchResponseV3_Result_Value{} }
func (m *SearchResponseV3_Result_Value) String() string { return proto.CompactTextString(m) }
func (*SearchResponseV3_Result_Value) ProtoMessage()    {}
func (*SearchResponseV3_Result_Value) Descriptor() ([]byte, []int) {
	return fileDescriptor0, []int{0, 0, 0}
}

type isSearchResponseV3_Result_Value_Val interface {
	isSearchResponseV3_Result_Value_Val()
}

type SearchResponseV3_Result_Value_Str struct {
	Str string `protobuf:"bytes,1,opt,name=str,oneof"`
}
type SearchResponseV3_Result_Value_Int struct {
	Int int64 `protobuf:"varint,2,opt,name=int,oneof"`
}
type SearchResponseV3_Result_Value_Real struct {
	Real float64 `protobuf:"fixed64,3,opt,name=real,oneof"`
}

func (*SearchResponseV3_Result_Value_Str) isSearchResponseV3_Result_Value_Val()  {}
func (*SearchResponseV3_Result_Value_Int) isSearchResponseV3_Result_Value_Val()  {}
func (*SearchResponseV3_Result_Value_Real) isSearchResponseV3_Result_Value_Val() {}

func (m *SearchResponseV3_Result_Value) GetVal() isSearchResponseV3_Result_Value_Val {
	if m != nil {
		return m.Val
	}
	return nil
}

func (m *SearchResponseV3_Result_Value) GetStr() string {
	if x, ok := m.GetVal().(*SearchResponseV3_Result_Value_Str); ok {
		return x.Str
	}
	return ""
}

func (m *SearchResponseV3_Result_Value) GetInt() int64 {
	if x, ok := m.GetVal().(*SearchResponseV3_Result_Value_Int); ok {
		return x.Int
	}
	return 0
}

func (m *SearchResponseV3_Result_Value) GetReal() float64 {
	if x, ok := m.GetVal().(*SearchResponseV3_Result_Value_Real); ok {
		return x.Real
	}
	return 0
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*SearchResponseV3_Result_Value) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _SearchResponseV3_Result_Value_OneofMarshaler, _SearchResponseV3_Result_Value_OneofUnmarshaler, _SearchResponseV3_Result_Value_OneofSizer, []interface{}{
		(*SearchResponseV3_Result_Value_Str)(nil),
		(*SearchResponseV3_Result_Value_Int)(nil),
		(*SearchResponseV3_Result_Value_Real)(nil),
	}
}

func _SearchResponseV3_Result_Value_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*SearchResponseV3_Result_Value)
	// val
	switch x := m.Val.(type) {
	case *SearchResponseV3_Result_Value_Str:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.Str)
	case *SearchResponseV3_Result_Value_Int:
		b.EncodeVarint(2<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.Int))
	case *SearchResponseV3_Result_Value_Real:
		b.EncodeVarint(3<<3 | proto.WireFixed64)
		b.EncodeFixed64(math.Float64bits(x.Real))
	case nil:
	default:
		return fmt.Errorf("SearchResponseV3_Result_Value.Val has unexpected type %T", x)
	}
	return nil
}

func _SearchResponseV3_Result_Value_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*SearchResponseV3_Result_Value)
	switch tag {
	case 1: // val.str
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.Val = &SearchResponseV3_Result_Value_Str{x}
		return true, err
	case 2: // val.int
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Val = &SearchResponseV3_Result_Value_Int{int64(x)}
		return true, err
	case 3: // val.real
		if wire != proto.WireFixed64 {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeFixed64()
		m.Val = &SearchResponseV3_Result_Value_Real{math.Float64frombits(x)}
		return true, err
	default:
		return false, nil
	}
}

func _SearchResponseV3_Result_Value_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*SearchResponseV3_Result_Value)
	// val
	switch x := m.Val.(type) {
	case *SearchResponseV3_Result_Value_Str:
		n += proto.SizeVarint(1<<3 | proto.WireBytes)
		n += proto.SizeVarint(uint64(len(x.Str)))
		n += len(x.Str)
	case *SearchResponseV3_Result_Value_Int:
		n += proto.SizeVarint(2<<3 | proto.WireVarint)
		n += proto.SizeVarint(uint64(x.Int))
	case *SearchResponseV3_Result_Value_Real:
		n += proto.SizeVarint(3<<3 | proto.WireFixed64)
		n += 8
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type SearchRequestV3 struct {
	Query string `protobuf:"bytes,1,opt,name=query" json:"query,omitempty"`
}

func (m *SearchRequestV3) Reset()                    { *m = SearchRequestV3{} }
func (m *SearchRequestV3) String() string            { return proto.CompactTextString(m) }
func (*SearchRequestV3) ProtoMessage()               {}
func (*SearchRequestV3) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{1} }

func (m *SearchRequestV3) GetQuery() string {
	if m != nil {
		return m.Query
	}
	return ""
}

func init() {
	proto.RegisterType((*SearchResponseV3)(nil), "grpc.testingv3.SearchResponseV3")
	proto.RegisterType((*SearchResponseV3_Result)(nil), "grpc.testingv3.SearchResponseV3.Result")
	proto.RegisterType((*SearchResponseV3_Result_Value)(nil), "grpc.testingv3.SearchResponseV3.Result.Value")
	proto.RegisterType((*SearchRequestV3)(nil), "grpc.testingv3.SearchRequestV3")
	proto.RegisterEnum("grpc.testingv3.SearchResponseV3_State", SearchResponseV3_State_name, SearchResponseV3_State_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion3

// Client API for SearchServiceV3 service

type SearchServiceV3Client interface {
	Search(ctx context.Context, in *SearchRequestV3, opts ...grpc.CallOption) (*SearchResponseV3, error)
	StreamingSearch(ctx context.Context, opts ...grpc.CallOption) (SearchServiceV3_StreamingSearchClient, error)
}

type searchServiceV3Client struct {
	cc *grpc.ClientConn
}

func NewSearchServiceV3Client(cc *grpc.ClientConn) SearchServiceV3Client {
	return &searchServiceV3Client{cc}
}

func (c *searchServiceV3Client) Search(ctx context.Context, in *SearchRequestV3, opts ...grpc.CallOption) (*SearchResponseV3, error) {
	out := new(SearchResponseV3)
	err := grpc.Invoke(ctx, "/grpc.testingv3.SearchServiceV3/Search", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchServiceV3Client) StreamingSearch(ctx context.Context, opts ...grpc.CallOption) (SearchServiceV3_StreamingSearchClient, error) {
	stream, err := grpc.NewClientStream(ctx, &_SearchServiceV3_serviceDesc.Streams[0], c.cc, "/grpc.testingv3.SearchServiceV3/StreamingSearch", opts...)
	if err != nil {
		return nil, err
	}
	x := &searchServiceV3StreamingSearchClient{stream}
	return x, nil
}

type SearchServiceV3_StreamingSearchClient interface {
	Send(*SearchRequestV3) error
	Recv() (*SearchResponseV3, error)
	grpc.ClientStream
}

type searchServiceV3StreamingSearchClient struct {
	grpc.ClientStream
}

func (x *searchServiceV3StreamingSearchClient) Send(m *SearchRequestV3) error {
	return x.ClientStream.SendMsg(m)
}

func (x *searchServiceV3StreamingSearchClient) Recv() (*SearchResponseV3, error) {
	m := new(SearchResponseV3)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// Server API for SearchServiceV3 service

type SearchServiceV3Server interface {
	Search(context.Context, *SearchRequestV3) (*SearchResponseV3, error)
	StreamingSearch(SearchServiceV3_StreamingSearchServer) error
}

func RegisterSearchServiceV3Server(s *grpc.Server, srv SearchServiceV3Server) {
	s.RegisterService(&_SearchServiceV3_serviceDesc, srv)
}

func _SearchServiceV3_Search_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchRequestV3)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SearchServiceV3Server).Search(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpc.testingv3.SearchServiceV3/Search",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SearchServiceV3Server).Search(ctx, req.(*SearchRequestV3))
	}
	return interceptor(ctx, in, info, handler)
}

func _SearchServiceV3_StreamingSearch_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(SearchServiceV3Server).StreamingSearch(&searchServiceV3StreamingSearchServer{stream})
}

type SearchServiceV3_StreamingSearchServer interface {
	Send(*SearchResponseV3) error
	Recv() (*SearchRequestV3, error)
	grpc.ServerStream
}

type searchServiceV3StreamingSearchServer struct {
	grpc.ServerStream
}

func (x *searchServiceV3StreamingSearchServer) Send(m *SearchResponseV3) error {
	return x.ServerStream.SendMsg(m)
}

func (x *searchServiceV3StreamingSearchServer) Recv() (*SearchRequestV3, error) {
	m := new(SearchRequestV3)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _SearchServiceV3_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpc.testingv3.SearchServiceV3",
	HandlerType: (*SearchServiceV3Server)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Search",
			Handler:    _SearchServiceV3_Search_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "StreamingSearch",
			Handler:       _SearchServiceV3_StreamingSearch_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: fileDescriptor0,
}

func init() { proto.RegisterFile("testv3.proto", fileDescriptor0) }

var fileDescriptor0 = []byte{
	// 416 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x93, 0xd1, 0x6a, 0xd4, 0x40,
	0x14, 0x86, 0x77, 0x36, 0x9b, 0x6d, 0xf7, 0xac, 0xb6, 0x61, 0xe8, 0x45, 0xc8, 0x8d, 0x61, 0x2f,
	0x6c, 0x10, 0x0c, 0x92, 0x20, 0x88, 0x78, 0x53, 0x65, 0x65, 0xa1, 0x75, 0xc5, 0x89, 0xae, 0xde,
	0x8e, 0xeb, 0x61, 0x8d, 0x4d, 0xb3, 0xe9, 0xcc, 0x49, 0x60, 0x9f, 0xc5, 0x17, 0xf1, 0x55, 0x7c,
	0x1b, 0x99, 0x99, 0xa6, 0x50, 0x41, 0xba, 0x17, 0xde, 0xcd, 0x7f, 0x38, 0xff, 0x37, 0xff, 0x3f,
	0x24, 0xf0, 0x80, 0x50, 0x53, 0x97, 0xa7, 0x8d, 0xda, 0xd2, 0x96, 0x1f, 0x6d, 0x54, 0xb3, 0x4e,
	0xcd, 0xa8, 0xac, 0x37, 0x5d, 0x3e, 0xfb, 0x39, 0x82, 0xa0, 0x40, 0xa9, 0xd6, 0xdf, 0x05, 0xea,
	0x66, 0x5b, 0x6b, 0x5c, 0xe5, 0xfc, 0x0c, 0x0e, 0x14, 0xea, 0xb6, 0x22, 0x1d, 0xb2, 0xd8, 0x4b,
	0xa6, 0xd9, 0x69, 0x7a, 0xd7, 0x96, 0xfe, 0x6d, 0x49, 0x85, 0xdd, 0x17, 0xbd, 0x8f, 0xbf, 0x02,
	0x5f, 0x93, 0x24, 0x0c, 0x87, 0x31, 0x4b, 0x8e, 0xb2, 0xc7, 0xf7, 0x02, 0x0a, 0xb3, 0x2d, 0x9c,
	0x29, 0xfa, 0x3d, 0x84, 0xb1, 0x23, 0xf2, 0x00, 0xbc, 0x56, 0x55, 0x21, 0x8b, 0x59, 0x32, 0x11,
	0xe6, 0xc8, 0x4f, 0xc0, 0xa7, 0x92, 0x2a, 0x87, 0x9e, 0x08, 0x27, 0x78, 0x04, 0x87, 0xba, 0x2e,
	0x9b, 0x06, 0x49, 0x87, 0x5e, 0xec, 0x25, 0x13, 0x71, 0xab, 0xf9, 0x07, 0x38, 0xbc, 0x42, 0x92,
	0xdf, 0x24, 0xc9, 0x70, 0x64, 0x0b, 0x3d, 0xdf, 0xb3, 0x50, 0xfa, 0xee, 0xc6, 0x37, 0xaf, 0x49,
	0xed, 0xc4, 0x2d, 0x26, 0xba, 0x00, 0x7f, 0x25, 0xab, 0x16, 0x39, 0x07, 0x4f, 0x93, 0x72, 0xf9,
	0x16, 0x03, 0x61, 0x84, 0x99, 0x95, 0x35, 0xd9, 0x7c, 0x9e, 0x99, 0x95, 0x35, 0xf1, 0x13, 0x18,
	0x29, 0x94, 0x55, 0xe8, 0xc5, 0x2c, 0x61, 0x8b, 0x81, 0xb0, 0xea, 0xb5, 0x0f, 0x5e, 0x27, 0xab,
	0xe8, 0x07, 0x3c, 0xbc, 0x73, 0x91, 0x69, 0x7d, 0x89, 0xbb, 0xbe, 0xf5, 0x25, 0xee, 0xf8, 0x1b,
	0xf0, 0x3b, 0x73, 0xa1, 0xa5, 0x4e, 0xb3, 0xa7, 0xfb, 0x16, 0xb0, 0x29, 0x85, 0xf3, 0xbe, 0x1c,
	0xbe, 0x60, 0xb3, 0x27, 0xe0, 0xdb, 0xb7, 0xe6, 0x53, 0x38, 0xf8, 0xb4, 0x3c, 0x5f, 0xbe, 0xff,
	0xbc, 0x0c, 0x06, 0x7c, 0x02, 0xfe, 0x5b, 0x31, 0x2f, 0x16, 0x01, 0x33, 0xc7, 0xe2, 0xe3, 0xd9,
	0xc5, 0x3c, 0x18, 0xce, 0x4e, 0xe1, 0xb8, 0xe7, 0x5e, 0xb7, 0xa8, 0x69, 0x95, 0x9b, 0xd7, 0xbf,
	0x6e, 0x51, 0xf5, 0xd9, 0x9c, 0xc8, 0x7e, 0xb1, 0x7e, 0xb3, 0x40, 0xd5, 0x95, 0x6b, 0xf3, 0x15,
	0x9d, 0xc3, 0xd8, 0x8d, 0xf8, 0xa3, 0x7f, 0x85, 0xbd, 0x81, 0x46, 0xf1, 0x7d, 0x6d, 0xf8, 0x17,
	0x38, 0x2e, 0x48, 0xa1, 0xbc, 0x2a, 0xeb, 0xcd, 0x7f, 0xa3, 0x26, 0xec, 0x19, 0xfb, 0x3a, 0xb6,
	0x3f, 0x46, 0xfe, 0x27, 0x00, 0x00, 0xff, 0xff, 0xed, 0xa2, 0x8d, 0x75, 0x28, 0x03, 0x00, 0x00,
}
