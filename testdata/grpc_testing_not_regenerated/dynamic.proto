/*
 * Copyright 2022 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

syntax = "proto3";

package grpc.testing;

option go_package = "google.golang.org/grpc/testdata/grpc_testing_not_regenerated";

message DynamicRes {}

message DynamicReq {}

// DynamicService is used to test reflection on dynamically constructed protocol
// buffer messages.
service DynamicService {
  // DynamicMessage1 is a test RPC for dynamically constructed protobufs.
  rpc DynamicMessage1(DynamicReq) returns (DynamicRes);
}
