# protoc-gen-go-grpc

This tool generates Go language bindings of `service`s in protobuf definition
files for gRPC.  For usage information, please see our [quick start
guide](https://grpc.io/docs/languages/go/quickstart/).

## Future-proofing services

By default, to register services using the methods generated by this tool, the
service implementations must embed the corresponding
`Unimplemented<ServiceName>Server` for future compatibility.  This is a behavior
change from the grpc code generator previously included with `protoc-gen-go`.
To restore this behavior, set the option `require_unimplemented_servers=false`.
E.g.:

```sh
  protoc --go-grpc_out=. --go-grpc_opt=require_unimplemented_servers=false[,other options...] \
```

Note that this is not recommended, and the option is only provided to restore
backward compatibility with previously-generated code.

When embedding the `Unimplemented<ServiceName>Server` in a struct that
implements the service, it should be embedded by _value_ instead of as a
_pointer_.  If it is embedded as a pointer, it must be assigned to a valid,
non-nil pointer or else unimplemented methods would panic when called.  This is
tested at service registration time, and will lead to a panic in
`Register<ServiceName>Server` if it is not embedded properly.
