/*
 *
 * Copyright 2017 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package codes

import (
	"encoding/json"
	"strings"
	"testing"

	"github.com/google/go-cmp/cmp"
	cpb "google.golang.org/genproto/googleapis/rpc/code"
	"google.golang.org/grpc/internal/grpctest"
)

type s struct {
	grpctest.Tester
}

func Test(t *testing.T) {
	grpctest.RunSubTests(t, s{})
}

func (s) TestUnmarshalJSON(t *testing.T) {
	for s, v := range cpb.Code_value {
		want := Code(v)
		var got Code
		if err := got.UnmarshalJSON([]byte(`"` + s + `"`)); err != nil || got != want {
			t.Errorf("got.UnmarshalJSON(%q) = %v; want <nil>.  got=%v; want %v", s, err, got, want)
		}
	}
}

func (s) TestJSONUnmarshal(t *testing.T) {
	var got []Code
	want := []Code{OK, NotFound, Internal, Canceled}
	in := `["OK", "NOT_FOUND", "INTERNAL", "CANCELLED"]`
	err := json.Unmarshal([]byte(in), &got)
	if err != nil || !cmp.Equal(got, want) {
		t.Fatalf("json.Unmarshal(%q, &got) = %v; want <nil>.  got=%v; want %v", in, err, got, want)
	}
}

func (s) TestUnmarshalJSON_NilReceiver(t *testing.T) {
	var got *Code
	in := OK.String()
	if err := got.UnmarshalJSON([]byte(in)); err == nil {
		t.Errorf("got.UnmarshalJSON(%q) = nil; want <non-nil>.  got=%v", in, got)
	}
}

func (s) TestUnmarshalJSON_UnknownInput(t *testing.T) {
	var got Code
	for _, in := range [][]byte{[]byte(""), []byte("xxx"), []byte("Code(17)"), nil} {
		if err := got.UnmarshalJSON([]byte(in)); err == nil {
			t.Errorf("got.UnmarshalJSON(%q) = nil; want <non-nil>.  got=%v", in, got)
		}
	}
}

func (s) TestUnmarshalJSON_MarshalUnmarshal(t *testing.T) {
	for i := 0; i < _maxCode; i++ {
		var cUnMarshaled Code
		c := Code(i)

		cJSON, err := json.Marshal(c)
		if err != nil {
			t.Errorf("marshalling %q failed: %v", c, err)
		}

		if err := json.Unmarshal(cJSON, &cUnMarshaled); err != nil {
			t.Errorf("unmarshalling code failed: %s", err)
		}

		if c != cUnMarshaled {
			t.Errorf("code is %q after marshalling/unmarshalling, expected %q", cUnMarshaled, c)
		}
	}
}

func (s) TestUnmarshalJSON_InvalidIntegerCode(t *testing.T) {
	const wantErr = "invalid code: 200" // for integer invalid code, expect integer value in error message

	var got Code
	if err := got.UnmarshalJSON([]byte("200")); !strings.Contains(err.Error(), wantErr) {
		t.Errorf("got.UnmarshalJSON(200) = %v; wantErr: %v", err, wantErr)
	}
}
