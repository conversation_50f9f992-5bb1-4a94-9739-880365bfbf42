//go:build !grpcgoid
// +build !grpcgoid

/*
 *
 * Copyright 2019 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package profiling

// This dummy function always returns 0. In some modified dev environments,
// this may be replaced with a call to a function in a modified Go runtime that
// retrieves the goroutine ID efficiently. See goid_modified.go for a different
// version of goId that requires a grpcgoid build tag to compile.
func goid() int64 {
	return 0
}
