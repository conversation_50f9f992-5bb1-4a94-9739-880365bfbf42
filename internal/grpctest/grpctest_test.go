/*
 *
 * Copyright 2018 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package grpctest

import (
	"reflect"
	"testing"
)

type tRunST struct {
	setup, test, teardown bool
}

func (t *tRunST) Setup(*testing.T) {
	t.setup = true
}
func (t *tRunST) TestSubTest(*testing.T) {
	t.test = true
}
func (t *tRunST) Teardown(*testing.T) {
	t.teardown = true
}

func TestRunSubTests(t *testing.T) {
	x := &tRunST{}
	RunSubTests(t, x)
	if want := (&tRunST{setup: true, test: true, teardown: true}); !reflect.DeepEqual(x, want) {
		t.Fatalf("x = %v; want all fields true", x)
	}
}
