/*
 *
 * Copyright 2023 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package testutils

import (
	"fmt"
	"net/url"
)

// MustParseURL attempts to parse the provided target using url.Parse()
// and panics if parsing fails.
func MustParseURL(target string) *url.URL {
	u, err := url.Parse(target)
	if err != nil {
		panic(fmt.Sprintf("Error parsing target(%s): %v", target, err))
	}
	return u
}
