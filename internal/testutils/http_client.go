/*
 *
 * Copyright 2020 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package testutils

import (
	"context"
	"net/http"
	"time"
)

// DefaultHTTPRequestTimeout is the default timeout value for the amount of time
// this client waits for a response to be pushed on RespChan before it fails the
// Do() call.
const DefaultHTTPRequestTimeout = 1 * time.Second

// FakeHTTPClient helps mock out HTTP calls made by the code under test. It
// makes HTTP requests made by the code under test available through a channel,
// and makes it possible to inject various responses.
type FakeHTTPClient struct {
	// Re<PERSON><PERSON><PERSON> exposes the HTTP.Request made by the code under test.
	ReqChan *Channel
	// RespChan is a channel on which this fake client accepts responses to be
	// sent to the code under test.
	RespChan *Channel
	// Err, if set, is returned by Do().
	Err error
	// RecvTimeout is the amount of the time this client waits for a response to
	// be pushed on RespChan before it fails the Do() call. If this field is
	// left unspecified, DefaultHTTPRequestTimeout is used.
	RecvTimeout time.Duration
}

// Do pushes req on ReqChan and returns the response available on RespChan.
func (fc *FakeHTTPClient) Do(req *http.Request) (*http.Response, error) {
	fc.ReqChan.Send(req)

	timeout := fc.RecvTimeout
	if timeout == 0 {
		timeout = DefaultHTTPRequestTimeout
	}
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	val, err := fc.RespChan.Receive(ctx)
	if err != nil {
		return nil, err
	}
	return val.(*http.Response), fc.Err
}
