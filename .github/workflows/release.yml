name: Release

on:
  release:
    types: [published]

permissions:
  contents: read

jobs:
  release:
    permissions:
      contents: write # to upload release asset (actions/upload-release-asset)

    name: Release cmd/protoc-gen-go-grpc
    runs-on: ubuntu-latest
    if: startsWith(github.event.release.tag_name, 'cmd/protoc-gen-go-grpc/')
    strategy:
      matrix:
        goos: [linux, darwin, windows]
        goarch: [386, amd64, arm64]
        exclude:
          - goos: darwin
            goarch: 386

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5

      - name: Download dependencies
        run: |
          cd cmd/protoc-gen-go-grpc
          go mod download

      - name: Prepare build directory
        run: |
          mkdir -p build/
          cp README.md build/
          cp LICENSE build/

      - name: Build
        env:
          GOOS: ${{ matrix.goos }}
          GOARCH: ${{ matrix.goarch }}
        run: |
          cd cmd/protoc-gen-go-grpc
          go build -trimpath -o $GITHUB_WORKSPACE/build

      - name: Create package
        id: package
        run: |
          PACKAGE_NAME=protoc-gen-go-grpc.${GITHUB_REF#refs/tags/cmd/protoc-gen-go-grpc/}.${{ matrix.goos }}.${{ matrix.goarch }}.tar.gz
          tar -czvf $PACKAGE_NAME -C build .
          echo "name=${PACKAGE_NAME}" >> $GITHUB_OUTPUT

      - name: Upload asset
        run: |
          gh release upload ${{ github.event.release.tag_name }} ./${{ steps.package.outputs.name }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
