/*
 * Copyright 2021 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package xdsclient

import "google.golang.org/grpc/resolver"

type clientKeyType string

const clientKey = clientKeyType("grpc.xds.internal.client.Client")

// FromResolverState returns the Client from state, or nil if not present.
func FromResolverState(state resolver.State) XDSClient {
	cs, _ := state.Attributes.Value(clientKey).(XDSClient)
	return cs
}

// SetClient sets c in state and returns the new state.
func SetClient(state resolver.State, c XDSClient) resolver.State {
	state.Attributes = state.Attributes.WithValue(clientK<PERSON>, c)
	return state
}
