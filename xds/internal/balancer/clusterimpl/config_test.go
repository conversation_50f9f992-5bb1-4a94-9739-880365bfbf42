/*
 *
 * Copyright 2020 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package clusterimpl

import (
	"testing"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/grpc/balancer"
	_ "google.golang.org/grpc/balancer/roundrobin"
	_ "google.golang.org/grpc/balancer/weightedtarget"
	internalserviceconfig "google.golang.org/grpc/internal/serviceconfig"
	"google.golang.org/grpc/internal/xds/bootstrap"
)

const (
	testJSONConfig = `{
  "cluster": "test_cluster",
  "edsServiceName": "test-eds",
  "lrsLoadReportingServer": {
    "server_uri": "trafficdirector.googleapis.com:443",
    "channel_creds": [ { "type": "google_default" } ]
  },
  "maxConcurrentRequests": 123,
  "dropCategories": [
    {
      "category": "drop-1",
      "requestsPerMillion": 314
    },
    {
      "category": "drop-2",
      "requestsPerMillion": 159
    }
  ],
  "childPolicy": [
    {
      "weighted_target_experimental": {
        "targets": {
          "wt-child-1": {
            "weight": 75,
            "childPolicy":[{"round_robin":{}}]
          },
          "wt-child-2": {
            "weight": 25,
            "childPolicy":[{"round_robin":{}}]
          }
        }
      }
    }
  ]
}`

	wtName = "weighted_target_experimental"
)

var (
	wtConfigParser = balancer.Get(wtName).(balancer.ConfigParser)
	wtConfigJSON   = `{
  "targets": {
    "wt-child-1": {
      "weight": 75,
      "childPolicy":[{"round_robin":{}}]
    },
    "wt-child-2": {
      "weight": 25,
      "childPolicy":[{"round_robin":{}}]
    }
  }
}`

	wtConfig, _ = wtConfigParser.ParseConfig([]byte(wtConfigJSON))
)

func TestParseConfig(t *testing.T) {
	testLRSServerConfig, err := bootstrap.ServerConfigForTesting(bootstrap.ServerConfigTestingOptions{
		URI:          "trafficdirector.googleapis.com:443",
		ChannelCreds: []bootstrap.ChannelCreds{{Type: "google_default"}},
	})
	if err != nil {
		t.Fatalf("Failed to create LRS server config for testing: %v", err)
	}

	tests := []struct {
		name    string
		js      string
		want    *LBConfig
		wantErr bool
	}{
		{
			name:    "empty json",
			js:      "",
			want:    nil,
			wantErr: true,
		},
		{
			name:    "bad json",
			js:      "{",
			want:    nil,
			wantErr: true,
		},
		{
			name: "OK",
			js:   testJSONConfig,
			want: &LBConfig{
				Cluster:               "test_cluster",
				EDSServiceName:        "test-eds",
				LoadReportingServer:   testLRSServerConfig,
				MaxConcurrentRequests: newUint32(123),
				DropCategories: []DropConfig{
					{Category: "drop-1", RequestsPerMillion: 314},
					{Category: "drop-2", RequestsPerMillion: 159},
				},
				ChildPolicy: &internalserviceconfig.BalancerConfig{
					Name:   wtName,
					Config: wtConfig,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseConfig([]byte(tt.js))
			if (err != nil) != tt.wantErr {
				t.Fatalf("parseConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
			if diff := cmp.Diff(tt.want, got); diff != "" {
				t.Errorf("parseConfig() got unexpected diff (-want, +got): %v", diff)
			}
		})
	}
}

func newUint32(i uint32) *uint32 {
	return &i
}
