/*
 *
 * Copyright 2020 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package priority

import (
	"testing"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/grpc/balancer/roundrobin"
	internalserviceconfig "google.golang.org/grpc/internal/serviceconfig"
)

func TestParseConfig(t *testing.T) {
	tests := []struct {
		name    string
		js      string
		want    *LBConfig
		wantErr bool
	}{
		{
			name: "child not found",
			js: `{
  "priorities": ["child-1", "child-2", "child-3"],
  "children": {
    "child-1": {"config": [{"round_robin":{}}]},
    "child-3": {"config": [{"round_robin":{}}]}
  }
}
			`,
			wantErr: true,
		},
		{
			name: "child not used",
			js: `{
  "priorities": ["child-1", "child-2"],
  "children": {
    "child-1": {"config": [{"round_robin":{}}]},
    "child-2": {"config": [{"round_robin":{}}]},
    "child-3": {"config": [{"round_robin":{}}]}
  }
}
			`,
			wantErr: true,
		},
		{
			name: "good",
			js: `{
  "priorities": ["child-1", "child-2", "child-3"],
  "children": {
    "child-1": {"config": [{"round_robin":{}}], "ignoreReresolutionRequests": true},
    "child-2": {"config": [{"round_robin":{}}]},
    "child-3": {"config": [{"round_robin":{}}]}
  }
}
			`,
			want: &LBConfig{
				Children: map[string]*Child{
					"child-1": {
						Config: &internalserviceconfig.BalancerConfig{
							Name: roundrobin.Name,
						},
						IgnoreReresolutionRequests: true,
					},
					"child-2": {
						Config: &internalserviceconfig.BalancerConfig{
							Name: roundrobin.Name,
						},
					},
					"child-3": {
						Config: &internalserviceconfig.BalancerConfig{
							Name: roundrobin.Name,
						},
					},
				},
				Priorities: []string{"child-1", "child-2", "child-3"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseConfig([]byte(tt.js))
			if (err != nil) != tt.wantErr {
				t.Errorf("parseConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want); diff != "" {
				t.Errorf("parseConfig() got = %v, want %v, diff: %s", got, tt.want, diff)
			}
		})
	}
}
