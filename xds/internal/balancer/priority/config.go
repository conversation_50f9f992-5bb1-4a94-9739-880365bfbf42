/*
 *
 * Copyright 2020 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package priority

import (
	"encoding/json"
	"fmt"

	internalserviceconfig "google.golang.org/grpc/internal/serviceconfig"
	"google.golang.org/grpc/serviceconfig"
)

// Child is a child of priority balancer.
type Child struct {
	Config                     *internalserviceconfig.BalancerConfig `json:"config,omitempty"`
	IgnoreReresolutionRequests bool                                  `json:"ignoreReresolutionRequests,omitempty"`
}

// LBConfig represents priority balancer's config.
type LBConfig struct {
	serviceconfig.LoadBalancingConfig `json:"-"`

	// Children is a map from the child balancer names to their configs. Child
	// names can be found in field Priorities.
	Children map[string]*Child `json:"children,omitempty"`
	// Priorities is a list of child balancer names. They are sorted from
	// highest priority to low. The type/config for each child can be found in
	// field Children, with the balancer name as the key.
	Priorities []string `json:"priorities,omitempty"`
}

func parseConfig(c json.RawMessage) (*LBConfig, error) {
	var cfg LBConfig
	if err := json.Unmarshal(c, &cfg); err != nil {
		return nil, err
	}

	prioritiesSet := make(map[string]bool)
	for _, name := range cfg.Priorities {
		if _, ok := cfg.Children[name]; !ok {
			return nil, fmt.Errorf("LB policy name %q found in Priorities field (%v) is not found in Children field (%+v)", name, cfg.Priorities, cfg.Children)
		}
		prioritiesSet[name] = true
	}
	for name := range cfg.Children {
		if _, ok := prioritiesSet[name]; !ok {
			return nil, fmt.Errorf("LB policy name %q found in Children field (%v) is not found in Priorities field (%+v)", name, cfg.Children, cfg.Priorities)
		}
	}
	return &cfg, nil
}
