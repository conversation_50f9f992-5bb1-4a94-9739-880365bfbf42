/*
 *
 * Copyright 2025 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package lrsclient

import (
	"google.golang.org/grpc/xds/internal/clients"
)

// Config is used to configure an LRS client. After one has been passed to the
// LRS client's New function, no part of it may modified. A Config may be
// reused; the lrsclient package will also not modify it.
type Config struct {
	// Node is the identity of the client application reporting load to the
	// LRS server.
	Node clients.Node

	// TransportBuilder is used to connect to the LRS server.
	TransportBuilder clients.TransportBuilder
}
