// Copyright 2017 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Messages used for performance tests that may not reference grpc directly for
// reasons of import cycles.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: test/codec_perf/perf.proto

package codec_perf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Buffer is a message that contains a body of bytes that is used to exercise
// encoding and decoding overheads.
type Buffer struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Body          []byte                 `protobuf:"bytes,1,opt,name=body,proto3" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Buffer) Reset() {
	*x = Buffer{}
	mi := &file_test_codec_perf_perf_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Buffer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Buffer) ProtoMessage() {}

func (x *Buffer) ProtoReflect() protoreflect.Message {
	mi := &file_test_codec_perf_perf_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Buffer.ProtoReflect.Descriptor instead.
func (*Buffer) Descriptor() ([]byte, []int) {
	return file_test_codec_perf_perf_proto_rawDescGZIP(), []int{0}
}

func (x *Buffer) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

var File_test_codec_perf_perf_proto protoreflect.FileDescriptor

const file_test_codec_perf_perf_proto_rawDesc = "" +
	"\n" +
	"\x1atest/codec_perf/perf.proto\x12\n" +
	"codec.perf\"\x1c\n" +
	"\x06Buffer\x12\x12\n" +
	"\x04body\x18\x01 \x01(\fR\x04bodyB(Z&google.golang.org/grpc/test/codec_perfb\x06proto3"

var (
	file_test_codec_perf_perf_proto_rawDescOnce sync.Once
	file_test_codec_perf_perf_proto_rawDescData []byte
)

func file_test_codec_perf_perf_proto_rawDescGZIP() []byte {
	file_test_codec_perf_perf_proto_rawDescOnce.Do(func() {
		file_test_codec_perf_perf_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_test_codec_perf_perf_proto_rawDesc), len(file_test_codec_perf_perf_proto_rawDesc)))
	})
	return file_test_codec_perf_perf_proto_rawDescData
}

var file_test_codec_perf_perf_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_test_codec_perf_perf_proto_goTypes = []any{
	(*Buffer)(nil), // 0: codec.perf.Buffer
}
var file_test_codec_perf_perf_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_test_codec_perf_perf_proto_init() }
func file_test_codec_perf_perf_proto_init() {
	if File_test_codec_perf_perf_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_test_codec_perf_perf_proto_rawDesc), len(file_test_codec_perf_perf_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_test_codec_perf_perf_proto_goTypes,
		DependencyIndexes: file_test_codec_perf_perf_proto_depIdxs,
		MessageInfos:      file_test_codec_perf_perf_proto_msgTypes,
	}.Build()
	File_test_codec_perf_perf_proto = out.File
	file_test_codec_perf_perf_proto_goTypes = nil
	file_test_codec_perf_perf_proto_depIdxs = nil
}
