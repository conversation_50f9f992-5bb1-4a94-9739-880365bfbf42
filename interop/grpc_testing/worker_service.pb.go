// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// An integration test service that covers all the method signature permutations
// of unary/streaming requests/responses.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: grpc/testing/worker_service.proto

package grpc_testing

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_grpc_testing_worker_service_proto protoreflect.FileDescriptor

const file_grpc_testing_worker_service_proto_rawDesc = "" +
	"\n" +
	"!grpc/testing/worker_service.proto\x12\fgrpc.testing\x1a\x1agrpc/testing/control.proto2\x97\x02\n" +
	"\rWorkerService\x12E\n" +
	"\tRunServer\x12\x18.grpc.testing.ServerArgs\x1a\x1a.grpc.testing.ServerStatus(\x010\x01\x12E\n" +
	"\tRunClient\x12\x18.grpc.testing.ClientArgs\x1a\x1a.grpc.testing.ClientStatus(\x010\x01\x12B\n" +
	"\tCoreCount\x12\x19.grpc.testing.CoreRequest\x1a\x1a.grpc.testing.CoreResponse\x124\n" +
	"\n" +
	"QuitWorker\x12\x12.grpc.testing.Void\x1a\x12.grpc.testing.VoidB'\n" +
	"\x0fio.grpc.testingB\x12WorkerServiceProtoP\x01b\x06proto3"

var file_grpc_testing_worker_service_proto_goTypes = []any{
	(*ServerArgs)(nil),   // 0: grpc.testing.ServerArgs
	(*ClientArgs)(nil),   // 1: grpc.testing.ClientArgs
	(*CoreRequest)(nil),  // 2: grpc.testing.CoreRequest
	(*Void)(nil),         // 3: grpc.testing.Void
	(*ServerStatus)(nil), // 4: grpc.testing.ServerStatus
	(*ClientStatus)(nil), // 5: grpc.testing.ClientStatus
	(*CoreResponse)(nil), // 6: grpc.testing.CoreResponse
}
var file_grpc_testing_worker_service_proto_depIdxs = []int32{
	0, // 0: grpc.testing.WorkerService.RunServer:input_type -> grpc.testing.ServerArgs
	1, // 1: grpc.testing.WorkerService.RunClient:input_type -> grpc.testing.ClientArgs
	2, // 2: grpc.testing.WorkerService.CoreCount:input_type -> grpc.testing.CoreRequest
	3, // 3: grpc.testing.WorkerService.QuitWorker:input_type -> grpc.testing.Void
	4, // 4: grpc.testing.WorkerService.RunServer:output_type -> grpc.testing.ServerStatus
	5, // 5: grpc.testing.WorkerService.RunClient:output_type -> grpc.testing.ClientStatus
	6, // 6: grpc.testing.WorkerService.CoreCount:output_type -> grpc.testing.CoreResponse
	3, // 7: grpc.testing.WorkerService.QuitWorker:output_type -> grpc.testing.Void
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_grpc_testing_worker_service_proto_init() }
func file_grpc_testing_worker_service_proto_init() {
	if File_grpc_testing_worker_service_proto != nil {
		return
	}
	file_grpc_testing_control_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_grpc_testing_worker_service_proto_rawDesc), len(file_grpc_testing_worker_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_grpc_testing_worker_service_proto_goTypes,
		DependencyIndexes: file_grpc_testing_worker_service_proto_depIdxs,
	}.Build()
	File_grpc_testing_worker_service_proto = out.File
	file_grpc_testing_worker_service_proto_goTypes = nil
	file_grpc_testing_worker_service_proto_depIdxs = nil
}
