// Copyright 2015-2016 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Message definitions to be used by integration test service definitions.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: grpc/testing/messages.proto

package grpc_testing

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The type of payload that should be returned.
type PayloadType int32

const (
	// Compressable text format.
	PayloadType_COMPRESSABLE PayloadType = 0
)

// Enum value maps for PayloadType.
var (
	PayloadType_name = map[int32]string{
		0: "COMPRESSABLE",
	}
	PayloadType_value = map[string]int32{
		"COMPRESSABLE": 0,
	}
)

func (x PayloadType) Enum() *PayloadType {
	p := new(PayloadType)
	*p = x
	return p
}

func (x PayloadType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PayloadType) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_testing_messages_proto_enumTypes[0].Descriptor()
}

func (PayloadType) Type() protoreflect.EnumType {
	return &file_grpc_testing_messages_proto_enumTypes[0]
}

func (x PayloadType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PayloadType.Descriptor instead.
func (PayloadType) EnumDescriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{0}
}

// The type of route that a client took to reach a server w.r.t. gRPCLB.
// The server must fill in "fallback" if it detects that the RPC reached
// the server via the "gRPCLB fallback" path, and "backend" if it detects
// that the RPC reached the server via "gRPCLB backend" path (i.e. if it got
// the address of this server from the gRPCLB server BalanceLoad RPC). Exactly
// how this detection is done is context and server dependent.
type GrpclbRouteType int32

const (
	// Server didn't detect the route that a client took to reach it.
	GrpclbRouteType_GRPCLB_ROUTE_TYPE_UNKNOWN GrpclbRouteType = 0
	// Indicates that a client reached a server via gRPCLB fallback.
	GrpclbRouteType_GRPCLB_ROUTE_TYPE_FALLBACK GrpclbRouteType = 1
	// Indicates that a client reached a server as a gRPCLB-given backend.
	GrpclbRouteType_GRPCLB_ROUTE_TYPE_BACKEND GrpclbRouteType = 2
)

// Enum value maps for GrpclbRouteType.
var (
	GrpclbRouteType_name = map[int32]string{
		0: "GRPCLB_ROUTE_TYPE_UNKNOWN",
		1: "GRPCLB_ROUTE_TYPE_FALLBACK",
		2: "GRPCLB_ROUTE_TYPE_BACKEND",
	}
	GrpclbRouteType_value = map[string]int32{
		"GRPCLB_ROUTE_TYPE_UNKNOWN":  0,
		"GRPCLB_ROUTE_TYPE_FALLBACK": 1,
		"GRPCLB_ROUTE_TYPE_BACKEND":  2,
	}
)

func (x GrpclbRouteType) Enum() *GrpclbRouteType {
	p := new(GrpclbRouteType)
	*p = x
	return p
}

func (x GrpclbRouteType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GrpclbRouteType) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_testing_messages_proto_enumTypes[1].Descriptor()
}

func (GrpclbRouteType) Type() protoreflect.EnumType {
	return &file_grpc_testing_messages_proto_enumTypes[1]
}

func (x GrpclbRouteType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GrpclbRouteType.Descriptor instead.
func (GrpclbRouteType) EnumDescriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{1}
}

type LoadBalancerStatsResponse_MetadataType int32

const (
	LoadBalancerStatsResponse_UNKNOWN  LoadBalancerStatsResponse_MetadataType = 0
	LoadBalancerStatsResponse_INITIAL  LoadBalancerStatsResponse_MetadataType = 1
	LoadBalancerStatsResponse_TRAILING LoadBalancerStatsResponse_MetadataType = 2
)

// Enum value maps for LoadBalancerStatsResponse_MetadataType.
var (
	LoadBalancerStatsResponse_MetadataType_name = map[int32]string{
		0: "UNKNOWN",
		1: "INITIAL",
		2: "TRAILING",
	}
	LoadBalancerStatsResponse_MetadataType_value = map[string]int32{
		"UNKNOWN":  0,
		"INITIAL":  1,
		"TRAILING": 2,
	}
)

func (x LoadBalancerStatsResponse_MetadataType) Enum() *LoadBalancerStatsResponse_MetadataType {
	p := new(LoadBalancerStatsResponse_MetadataType)
	*p = x
	return p
}

func (x LoadBalancerStatsResponse_MetadataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoadBalancerStatsResponse_MetadataType) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_testing_messages_proto_enumTypes[2].Descriptor()
}

func (LoadBalancerStatsResponse_MetadataType) Type() protoreflect.EnumType {
	return &file_grpc_testing_messages_proto_enumTypes[2]
}

func (x LoadBalancerStatsResponse_MetadataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoadBalancerStatsResponse_MetadataType.Descriptor instead.
func (LoadBalancerStatsResponse_MetadataType) EnumDescriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{13, 0}
}

// Type of RPCs to send.
type ClientConfigureRequest_RpcType int32

const (
	ClientConfigureRequest_EMPTY_CALL ClientConfigureRequest_RpcType = 0
	ClientConfigureRequest_UNARY_CALL ClientConfigureRequest_RpcType = 1
)

// Enum value maps for ClientConfigureRequest_RpcType.
var (
	ClientConfigureRequest_RpcType_name = map[int32]string{
		0: "EMPTY_CALL",
		1: "UNARY_CALL",
	}
	ClientConfigureRequest_RpcType_value = map[string]int32{
		"EMPTY_CALL": 0,
		"UNARY_CALL": 1,
	}
)

func (x ClientConfigureRequest_RpcType) Enum() *ClientConfigureRequest_RpcType {
	p := new(ClientConfigureRequest_RpcType)
	*p = x
	return p
}

func (x ClientConfigureRequest_RpcType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientConfigureRequest_RpcType) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_testing_messages_proto_enumTypes[3].Descriptor()
}

func (ClientConfigureRequest_RpcType) Type() protoreflect.EnumType {
	return &file_grpc_testing_messages_proto_enumTypes[3]
}

func (x ClientConfigureRequest_RpcType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClientConfigureRequest_RpcType.Descriptor instead.
func (ClientConfigureRequest_RpcType) EnumDescriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{16, 0}
}

type HookRequest_HookRequestCommand int32

const (
	// Default value
	HookRequest_UNSPECIFIED HookRequest_HookRequestCommand = 0
	// Start the HTTP endpoint
	HookRequest_START HookRequest_HookRequestCommand = 1
	// Stop
	HookRequest_STOP HookRequest_HookRequestCommand = 2
	// Return from HTTP GET/POST
	HookRequest_RETURN HookRequest_HookRequestCommand = 3
)

// Enum value maps for HookRequest_HookRequestCommand.
var (
	HookRequest_HookRequestCommand_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "START",
		2: "STOP",
		3: "RETURN",
	}
	HookRequest_HookRequestCommand_value = map[string]int32{
		"UNSPECIFIED": 0,
		"START":       1,
		"STOP":        2,
		"RETURN":      3,
	}
)

func (x HookRequest_HookRequestCommand) Enum() *HookRequest_HookRequestCommand {
	p := new(HookRequest_HookRequestCommand)
	*p = x
	return p
}

func (x HookRequest_HookRequestCommand) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HookRequest_HookRequestCommand) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_testing_messages_proto_enumTypes[4].Descriptor()
}

func (HookRequest_HookRequestCommand) Type() protoreflect.EnumType {
	return &file_grpc_testing_messages_proto_enumTypes[4]
}

func (x HookRequest_HookRequestCommand) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HookRequest_HookRequestCommand.Descriptor instead.
func (HookRequest_HookRequestCommand) EnumDescriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{21, 0}
}

// TODO(dgq): Go back to using well-known types once
// https://github.com/grpc/grpc/issues/6980 has been fixed.
// import "google/protobuf/wrappers.proto";
type BoolValue struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The bool value.
	Value         bool `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BoolValue) Reset() {
	*x = BoolValue{}
	mi := &file_grpc_testing_messages_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BoolValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoolValue) ProtoMessage() {}

func (x *BoolValue) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoolValue.ProtoReflect.Descriptor instead.
func (*BoolValue) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{0}
}

func (x *BoolValue) GetValue() bool {
	if x != nil {
		return x.Value
	}
	return false
}

// A block of data, to simply increase gRPC message size.
type Payload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The type of data in body.
	Type PayloadType `protobuf:"varint,1,opt,name=type,proto3,enum=grpc.testing.PayloadType" json:"type,omitempty"`
	// Primary contents of payload.
	Body          []byte `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Payload) Reset() {
	*x = Payload{}
	mi := &file_grpc_testing_messages_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Payload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Payload) ProtoMessage() {}

func (x *Payload) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Payload.ProtoReflect.Descriptor instead.
func (*Payload) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{1}
}

func (x *Payload) GetType() PayloadType {
	if x != nil {
		return x.Type
	}
	return PayloadType_COMPRESSABLE
}

func (x *Payload) GetBody() []byte {
	if x != nil {
		return x.Body
	}
	return nil
}

// A protobuf representation for grpc status. This is used by test
// clients to specify a status that the server should attempt to return.
type EchoStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EchoStatus) Reset() {
	*x = EchoStatus{}
	mi := &file_grpc_testing_messages_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EchoStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoStatus) ProtoMessage() {}

func (x *EchoStatus) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoStatus.ProtoReflect.Descriptor instead.
func (*EchoStatus) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{2}
}

func (x *EchoStatus) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *EchoStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// Unary request.
type SimpleRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Desired payload type in the response from the server.
	// If response_type is RANDOM, server randomly chooses one from other formats.
	ResponseType PayloadType `protobuf:"varint,1,opt,name=response_type,json=responseType,proto3,enum=grpc.testing.PayloadType" json:"response_type,omitempty"`
	// Desired payload size in the response from the server.
	ResponseSize int32 `protobuf:"varint,2,opt,name=response_size,json=responseSize,proto3" json:"response_size,omitempty"`
	// Optional input payload sent along with the request.
	Payload *Payload `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload,omitempty"`
	// Whether SimpleResponse should include username.
	FillUsername bool `protobuf:"varint,4,opt,name=fill_username,json=fillUsername,proto3" json:"fill_username,omitempty"`
	// Whether SimpleResponse should include OAuth scope.
	FillOauthScope bool `protobuf:"varint,5,opt,name=fill_oauth_scope,json=fillOauthScope,proto3" json:"fill_oauth_scope,omitempty"`
	// Whether to request the server to compress the response. This field is
	// "nullable" in order to interoperate seamlessly with clients not able to
	// implement the full compression tests by introspecting the call to verify
	// the response's compression status.
	ResponseCompressed *BoolValue `protobuf:"bytes,6,opt,name=response_compressed,json=responseCompressed,proto3" json:"response_compressed,omitempty"`
	// Whether server should return a given status
	ResponseStatus *EchoStatus `protobuf:"bytes,7,opt,name=response_status,json=responseStatus,proto3" json:"response_status,omitempty"`
	// Whether the server should expect this request to be compressed.
	ExpectCompressed *BoolValue `protobuf:"bytes,8,opt,name=expect_compressed,json=expectCompressed,proto3" json:"expect_compressed,omitempty"`
	// Whether SimpleResponse should include server_id.
	FillServerId bool `protobuf:"varint,9,opt,name=fill_server_id,json=fillServerId,proto3" json:"fill_server_id,omitempty"`
	// Whether SimpleResponse should include grpclb_route_type.
	FillGrpclbRouteType bool `protobuf:"varint,10,opt,name=fill_grpclb_route_type,json=fillGrpclbRouteType,proto3" json:"fill_grpclb_route_type,omitempty"`
	// If set the server should record this metrics report data for the current RPC.
	OrcaPerQueryReport *TestOrcaReport `protobuf:"bytes,11,opt,name=orca_per_query_report,json=orcaPerQueryReport,proto3" json:"orca_per_query_report,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *SimpleRequest) Reset() {
	*x = SimpleRequest{}
	mi := &file_grpc_testing_messages_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimpleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimpleRequest) ProtoMessage() {}

func (x *SimpleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimpleRequest.ProtoReflect.Descriptor instead.
func (*SimpleRequest) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{3}
}

func (x *SimpleRequest) GetResponseType() PayloadType {
	if x != nil {
		return x.ResponseType
	}
	return PayloadType_COMPRESSABLE
}

func (x *SimpleRequest) GetResponseSize() int32 {
	if x != nil {
		return x.ResponseSize
	}
	return 0
}

func (x *SimpleRequest) GetPayload() *Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *SimpleRequest) GetFillUsername() bool {
	if x != nil {
		return x.FillUsername
	}
	return false
}

func (x *SimpleRequest) GetFillOauthScope() bool {
	if x != nil {
		return x.FillOauthScope
	}
	return false
}

func (x *SimpleRequest) GetResponseCompressed() *BoolValue {
	if x != nil {
		return x.ResponseCompressed
	}
	return nil
}

func (x *SimpleRequest) GetResponseStatus() *EchoStatus {
	if x != nil {
		return x.ResponseStatus
	}
	return nil
}

func (x *SimpleRequest) GetExpectCompressed() *BoolValue {
	if x != nil {
		return x.ExpectCompressed
	}
	return nil
}

func (x *SimpleRequest) GetFillServerId() bool {
	if x != nil {
		return x.FillServerId
	}
	return false
}

func (x *SimpleRequest) GetFillGrpclbRouteType() bool {
	if x != nil {
		return x.FillGrpclbRouteType
	}
	return false
}

func (x *SimpleRequest) GetOrcaPerQueryReport() *TestOrcaReport {
	if x != nil {
		return x.OrcaPerQueryReport
	}
	return nil
}

// Unary response, as configured by the request.
type SimpleResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Payload to increase message size.
	Payload *Payload `protobuf:"bytes,1,opt,name=payload,proto3" json:"payload,omitempty"`
	// The user the request came from, for verifying authentication was
	// successful when the client expected it.
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	// OAuth scope.
	OauthScope string `protobuf:"bytes,3,opt,name=oauth_scope,json=oauthScope,proto3" json:"oauth_scope,omitempty"`
	// Server ID. This must be unique among different server instances,
	// but the same across all RPC's made to a particular server instance.
	ServerId string `protobuf:"bytes,4,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
	// gRPCLB Path.
	GrpclbRouteType GrpclbRouteType `protobuf:"varint,5,opt,name=grpclb_route_type,json=grpclbRouteType,proto3,enum=grpc.testing.GrpclbRouteType" json:"grpclb_route_type,omitempty"`
	// Server hostname.
	Hostname      string `protobuf:"bytes,6,opt,name=hostname,proto3" json:"hostname,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimpleResponse) Reset() {
	*x = SimpleResponse{}
	mi := &file_grpc_testing_messages_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimpleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimpleResponse) ProtoMessage() {}

func (x *SimpleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimpleResponse.ProtoReflect.Descriptor instead.
func (*SimpleResponse) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{4}
}

func (x *SimpleResponse) GetPayload() *Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *SimpleResponse) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *SimpleResponse) GetOauthScope() string {
	if x != nil {
		return x.OauthScope
	}
	return ""
}

func (x *SimpleResponse) GetServerId() string {
	if x != nil {
		return x.ServerId
	}
	return ""
}

func (x *SimpleResponse) GetGrpclbRouteType() GrpclbRouteType {
	if x != nil {
		return x.GrpclbRouteType
	}
	return GrpclbRouteType_GRPCLB_ROUTE_TYPE_UNKNOWN
}

func (x *SimpleResponse) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

// Client-streaming request.
type StreamingInputCallRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Optional input payload sent along with the request.
	Payload *Payload `protobuf:"bytes,1,opt,name=payload,proto3" json:"payload,omitempty"`
	// Whether the server should expect this request to be compressed. This field
	// is "nullable" in order to interoperate seamlessly with servers not able to
	// implement the full compression tests by introspecting the call to verify
	// the request's compression status.
	ExpectCompressed *BoolValue `protobuf:"bytes,2,opt,name=expect_compressed,json=expectCompressed,proto3" json:"expect_compressed,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *StreamingInputCallRequest) Reset() {
	*x = StreamingInputCallRequest{}
	mi := &file_grpc_testing_messages_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamingInputCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamingInputCallRequest) ProtoMessage() {}

func (x *StreamingInputCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamingInputCallRequest.ProtoReflect.Descriptor instead.
func (*StreamingInputCallRequest) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{5}
}

func (x *StreamingInputCallRequest) GetPayload() *Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *StreamingInputCallRequest) GetExpectCompressed() *BoolValue {
	if x != nil {
		return x.ExpectCompressed
	}
	return nil
}

// Client-streaming response.
type StreamingInputCallResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Aggregated size of payloads received from the client.
	AggregatedPayloadSize int32 `protobuf:"varint,1,opt,name=aggregated_payload_size,json=aggregatedPayloadSize,proto3" json:"aggregated_payload_size,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *StreamingInputCallResponse) Reset() {
	*x = StreamingInputCallResponse{}
	mi := &file_grpc_testing_messages_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamingInputCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamingInputCallResponse) ProtoMessage() {}

func (x *StreamingInputCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamingInputCallResponse.ProtoReflect.Descriptor instead.
func (*StreamingInputCallResponse) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{6}
}

func (x *StreamingInputCallResponse) GetAggregatedPayloadSize() int32 {
	if x != nil {
		return x.AggregatedPayloadSize
	}
	return 0
}

// Configuration for a particular response.
type ResponseParameters struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Desired payload sizes in responses from the server.
	Size int32 `protobuf:"varint,1,opt,name=size,proto3" json:"size,omitempty"`
	// Desired interval between consecutive responses in the response stream in
	// microseconds.
	IntervalUs int32 `protobuf:"varint,2,opt,name=interval_us,json=intervalUs,proto3" json:"interval_us,omitempty"`
	// Whether to request the server to compress the response. This field is
	// "nullable" in order to interoperate seamlessly with clients not able to
	// implement the full compression tests by introspecting the call to verify
	// the response's compression status.
	Compressed    *BoolValue `protobuf:"bytes,3,opt,name=compressed,proto3" json:"compressed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResponseParameters) Reset() {
	*x = ResponseParameters{}
	mi := &file_grpc_testing_messages_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseParameters) ProtoMessage() {}

func (x *ResponseParameters) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseParameters.ProtoReflect.Descriptor instead.
func (*ResponseParameters) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{7}
}

func (x *ResponseParameters) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ResponseParameters) GetIntervalUs() int32 {
	if x != nil {
		return x.IntervalUs
	}
	return 0
}

func (x *ResponseParameters) GetCompressed() *BoolValue {
	if x != nil {
		return x.Compressed
	}
	return nil
}

// Server-streaming request.
type StreamingOutputCallRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Desired payload type in the response from the server.
	// If response_type is RANDOM, the payload from each response in the stream
	// might be of different types. This is to simulate a mixed type of payload
	// stream.
	ResponseType PayloadType `protobuf:"varint,1,opt,name=response_type,json=responseType,proto3,enum=grpc.testing.PayloadType" json:"response_type,omitempty"`
	// Configuration for each expected response message.
	ResponseParameters []*ResponseParameters `protobuf:"bytes,2,rep,name=response_parameters,json=responseParameters,proto3" json:"response_parameters,omitempty"`
	// Optional input payload sent along with the request.
	Payload *Payload `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload,omitempty"`
	// Whether server should return a given status
	ResponseStatus *EchoStatus `protobuf:"bytes,7,opt,name=response_status,json=responseStatus,proto3" json:"response_status,omitempty"`
	// If set the server should update this metrics report data at the OOB server.
	OrcaOobReport *TestOrcaReport `protobuf:"bytes,8,opt,name=orca_oob_report,json=orcaOobReport,proto3" json:"orca_oob_report,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamingOutputCallRequest) Reset() {
	*x = StreamingOutputCallRequest{}
	mi := &file_grpc_testing_messages_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamingOutputCallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamingOutputCallRequest) ProtoMessage() {}

func (x *StreamingOutputCallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamingOutputCallRequest.ProtoReflect.Descriptor instead.
func (*StreamingOutputCallRequest) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{8}
}

func (x *StreamingOutputCallRequest) GetResponseType() PayloadType {
	if x != nil {
		return x.ResponseType
	}
	return PayloadType_COMPRESSABLE
}

func (x *StreamingOutputCallRequest) GetResponseParameters() []*ResponseParameters {
	if x != nil {
		return x.ResponseParameters
	}
	return nil
}

func (x *StreamingOutputCallRequest) GetPayload() *Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *StreamingOutputCallRequest) GetResponseStatus() *EchoStatus {
	if x != nil {
		return x.ResponseStatus
	}
	return nil
}

func (x *StreamingOutputCallRequest) GetOrcaOobReport() *TestOrcaReport {
	if x != nil {
		return x.OrcaOobReport
	}
	return nil
}

// Server-streaming response, as configured by the request and parameters.
type StreamingOutputCallResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Payload to increase response size.
	Payload       *Payload `protobuf:"bytes,1,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamingOutputCallResponse) Reset() {
	*x = StreamingOutputCallResponse{}
	mi := &file_grpc_testing_messages_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamingOutputCallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamingOutputCallResponse) ProtoMessage() {}

func (x *StreamingOutputCallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamingOutputCallResponse.ProtoReflect.Descriptor instead.
func (*StreamingOutputCallResponse) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{9}
}

func (x *StreamingOutputCallResponse) GetPayload() *Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

// For reconnect interop test only.
// Client tells server what reconnection parameters it used.
type ReconnectParams struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	MaxReconnectBackoffMs int32                  `protobuf:"varint,1,opt,name=max_reconnect_backoff_ms,json=maxReconnectBackoffMs,proto3" json:"max_reconnect_backoff_ms,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ReconnectParams) Reset() {
	*x = ReconnectParams{}
	mi := &file_grpc_testing_messages_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReconnectParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReconnectParams) ProtoMessage() {}

func (x *ReconnectParams) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReconnectParams.ProtoReflect.Descriptor instead.
func (*ReconnectParams) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{10}
}

func (x *ReconnectParams) GetMaxReconnectBackoffMs() int32 {
	if x != nil {
		return x.MaxReconnectBackoffMs
	}
	return 0
}

// For reconnect interop test only.
// Server tells client whether its reconnects are following the spec and the
// reconnect backoffs it saw.
type ReconnectInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Passed        bool                   `protobuf:"varint,1,opt,name=passed,proto3" json:"passed,omitempty"`
	BackoffMs     []int32                `protobuf:"varint,2,rep,packed,name=backoff_ms,json=backoffMs,proto3" json:"backoff_ms,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReconnectInfo) Reset() {
	*x = ReconnectInfo{}
	mi := &file_grpc_testing_messages_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReconnectInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReconnectInfo) ProtoMessage() {}

func (x *ReconnectInfo) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReconnectInfo.ProtoReflect.Descriptor instead.
func (*ReconnectInfo) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{11}
}

func (x *ReconnectInfo) GetPassed() bool {
	if x != nil {
		return x.Passed
	}
	return false
}

func (x *ReconnectInfo) GetBackoffMs() []int32 {
	if x != nil {
		return x.BackoffMs
	}
	return nil
}

type LoadBalancerStatsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Request stats for the next num_rpcs sent by client.
	NumRpcs int32 `protobuf:"varint,1,opt,name=num_rpcs,json=numRpcs,proto3" json:"num_rpcs,omitempty"`
	// If num_rpcs have not completed within timeout_sec, return partial results.
	TimeoutSec int32 `protobuf:"varint,2,opt,name=timeout_sec,json=timeoutSec,proto3" json:"timeout_sec,omitempty"`
	// Response header + trailer metadata entries we want the values of.
	// Matching of the keys is case-insensitive as per rfc7540#section-8.1.2
	// * (asterisk) is a special value that will return all metadata entries
	MetadataKeys  []string `protobuf:"bytes,3,rep,name=metadata_keys,json=metadataKeys,proto3" json:"metadata_keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadBalancerStatsRequest) Reset() {
	*x = LoadBalancerStatsRequest{}
	mi := &file_grpc_testing_messages_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadBalancerStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerStatsRequest) ProtoMessage() {}

func (x *LoadBalancerStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerStatsRequest.ProtoReflect.Descriptor instead.
func (*LoadBalancerStatsRequest) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{12}
}

func (x *LoadBalancerStatsRequest) GetNumRpcs() int32 {
	if x != nil {
		return x.NumRpcs
	}
	return 0
}

func (x *LoadBalancerStatsRequest) GetTimeoutSec() int32 {
	if x != nil {
		return x.TimeoutSec
	}
	return 0
}

func (x *LoadBalancerStatsRequest) GetMetadataKeys() []string {
	if x != nil {
		return x.MetadataKeys
	}
	return nil
}

type LoadBalancerStatsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The number of completed RPCs for each peer.
	RpcsByPeer map[string]int32 `protobuf:"bytes,1,rep,name=rpcs_by_peer,json=rpcsByPeer,proto3" json:"rpcs_by_peer,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// The number of RPCs that failed to record a remote peer.
	NumFailures  int32                                            `protobuf:"varint,2,opt,name=num_failures,json=numFailures,proto3" json:"num_failures,omitempty"`
	RpcsByMethod map[string]*LoadBalancerStatsResponse_RpcsByPeer `protobuf:"bytes,3,rep,name=rpcs_by_method,json=rpcsByMethod,proto3" json:"rpcs_by_method,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// All the metadata of all RPCs for each peer.
	MetadatasByPeer map[string]*LoadBalancerStatsResponse_MetadataByPeer `protobuf:"bytes,4,rep,name=metadatas_by_peer,json=metadatasByPeer,proto3" json:"metadatas_by_peer,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *LoadBalancerStatsResponse) Reset() {
	*x = LoadBalancerStatsResponse{}
	mi := &file_grpc_testing_messages_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadBalancerStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerStatsResponse) ProtoMessage() {}

func (x *LoadBalancerStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerStatsResponse.ProtoReflect.Descriptor instead.
func (*LoadBalancerStatsResponse) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{13}
}

func (x *LoadBalancerStatsResponse) GetRpcsByPeer() map[string]int32 {
	if x != nil {
		return x.RpcsByPeer
	}
	return nil
}

func (x *LoadBalancerStatsResponse) GetNumFailures() int32 {
	if x != nil {
		return x.NumFailures
	}
	return 0
}

func (x *LoadBalancerStatsResponse) GetRpcsByMethod() map[string]*LoadBalancerStatsResponse_RpcsByPeer {
	if x != nil {
		return x.RpcsByMethod
	}
	return nil
}

func (x *LoadBalancerStatsResponse) GetMetadatasByPeer() map[string]*LoadBalancerStatsResponse_MetadataByPeer {
	if x != nil {
		return x.MetadatasByPeer
	}
	return nil
}

// Request for retrieving a test client's accumulated stats.
type LoadBalancerAccumulatedStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadBalancerAccumulatedStatsRequest) Reset() {
	*x = LoadBalancerAccumulatedStatsRequest{}
	mi := &file_grpc_testing_messages_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadBalancerAccumulatedStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerAccumulatedStatsRequest) ProtoMessage() {}

func (x *LoadBalancerAccumulatedStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerAccumulatedStatsRequest.ProtoReflect.Descriptor instead.
func (*LoadBalancerAccumulatedStatsRequest) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{14}
}

// Accumulated stats for RPCs sent by a test client.
type LoadBalancerAccumulatedStatsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The total number of RPCs have ever issued for each type.
	// Deprecated: use stats_per_method.rpcs_started instead.
	//
	// Deprecated: Marked as deprecated in grpc/testing/messages.proto.
	NumRpcsStartedByMethod map[string]int32 `protobuf:"bytes,1,rep,name=num_rpcs_started_by_method,json=numRpcsStartedByMethod,proto3" json:"num_rpcs_started_by_method,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// The total number of RPCs have ever completed successfully for each type.
	// Deprecated: use stats_per_method.result instead.
	//
	// Deprecated: Marked as deprecated in grpc/testing/messages.proto.
	NumRpcsSucceededByMethod map[string]int32 `protobuf:"bytes,2,rep,name=num_rpcs_succeeded_by_method,json=numRpcsSucceededByMethod,proto3" json:"num_rpcs_succeeded_by_method,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// The total number of RPCs have ever failed for each type.
	// Deprecated: use stats_per_method.result instead.
	//
	// Deprecated: Marked as deprecated in grpc/testing/messages.proto.
	NumRpcsFailedByMethod map[string]int32 `protobuf:"bytes,3,rep,name=num_rpcs_failed_by_method,json=numRpcsFailedByMethod,proto3" json:"num_rpcs_failed_by_method,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// Per-method RPC statistics.  The key is the RpcType in string form; e.g.
	// 'EMPTY_CALL' or 'UNARY_CALL'
	StatsPerMethod map[string]*LoadBalancerAccumulatedStatsResponse_MethodStats `protobuf:"bytes,4,rep,name=stats_per_method,json=statsPerMethod,proto3" json:"stats_per_method,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *LoadBalancerAccumulatedStatsResponse) Reset() {
	*x = LoadBalancerAccumulatedStatsResponse{}
	mi := &file_grpc_testing_messages_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadBalancerAccumulatedStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerAccumulatedStatsResponse) ProtoMessage() {}

func (x *LoadBalancerAccumulatedStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerAccumulatedStatsResponse.ProtoReflect.Descriptor instead.
func (*LoadBalancerAccumulatedStatsResponse) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{15}
}

// Deprecated: Marked as deprecated in grpc/testing/messages.proto.
func (x *LoadBalancerAccumulatedStatsResponse) GetNumRpcsStartedByMethod() map[string]int32 {
	if x != nil {
		return x.NumRpcsStartedByMethod
	}
	return nil
}

// Deprecated: Marked as deprecated in grpc/testing/messages.proto.
func (x *LoadBalancerAccumulatedStatsResponse) GetNumRpcsSucceededByMethod() map[string]int32 {
	if x != nil {
		return x.NumRpcsSucceededByMethod
	}
	return nil
}

// Deprecated: Marked as deprecated in grpc/testing/messages.proto.
func (x *LoadBalancerAccumulatedStatsResponse) GetNumRpcsFailedByMethod() map[string]int32 {
	if x != nil {
		return x.NumRpcsFailedByMethod
	}
	return nil
}

func (x *LoadBalancerAccumulatedStatsResponse) GetStatsPerMethod() map[string]*LoadBalancerAccumulatedStatsResponse_MethodStats {
	if x != nil {
		return x.StatsPerMethod
	}
	return nil
}

// Configurations for a test client.
type ClientConfigureRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The types of RPCs the client sends.
	Types []ClientConfigureRequest_RpcType `protobuf:"varint,1,rep,packed,name=types,proto3,enum=grpc.testing.ClientConfigureRequest_RpcType" json:"types,omitempty"`
	// The collection of custom metadata to be attached to RPCs sent by the client.
	Metadata []*ClientConfigureRequest_Metadata `protobuf:"bytes,2,rep,name=metadata,proto3" json:"metadata,omitempty"`
	// The deadline to use, in seconds, for all RPCs.  If unset or zero, the
	// client will use the default from the command-line.
	TimeoutSec    int32 `protobuf:"varint,3,opt,name=timeout_sec,json=timeoutSec,proto3" json:"timeout_sec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientConfigureRequest) Reset() {
	*x = ClientConfigureRequest{}
	mi := &file_grpc_testing_messages_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientConfigureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientConfigureRequest) ProtoMessage() {}

func (x *ClientConfigureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientConfigureRequest.ProtoReflect.Descriptor instead.
func (*ClientConfigureRequest) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{16}
}

func (x *ClientConfigureRequest) GetTypes() []ClientConfigureRequest_RpcType {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *ClientConfigureRequest) GetMetadata() []*ClientConfigureRequest_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ClientConfigureRequest) GetTimeoutSec() int32 {
	if x != nil {
		return x.TimeoutSec
	}
	return 0
}

// Response for updating a test client's configuration.
type ClientConfigureResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientConfigureResponse) Reset() {
	*x = ClientConfigureResponse{}
	mi := &file_grpc_testing_messages_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientConfigureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientConfigureResponse) ProtoMessage() {}

func (x *ClientConfigureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientConfigureResponse.ProtoReflect.Descriptor instead.
func (*ClientConfigureResponse) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{17}
}

type MemorySize struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Rss           int64                  `protobuf:"varint,1,opt,name=rss,proto3" json:"rss,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MemorySize) Reset() {
	*x = MemorySize{}
	mi := &file_grpc_testing_messages_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemorySize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemorySize) ProtoMessage() {}

func (x *MemorySize) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemorySize.ProtoReflect.Descriptor instead.
func (*MemorySize) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{18}
}

func (x *MemorySize) GetRss() int64 {
	if x != nil {
		return x.Rss
	}
	return 0
}

// Metrics data the server will update and send to the client. It mirrors orca load report
// https://github.com/cncf/xds/blob/eded343319d09f30032952beda9840bbd3dcf7ac/xds/data/orca/v3/orca_load_report.proto#L15,
// but avoids orca dependency. Used by both per-query and out-of-band reporting tests.
type TestOrcaReport struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	CpuUtilization    float64                `protobuf:"fixed64,1,opt,name=cpu_utilization,json=cpuUtilization,proto3" json:"cpu_utilization,omitempty"`
	MemoryUtilization float64                `protobuf:"fixed64,2,opt,name=memory_utilization,json=memoryUtilization,proto3" json:"memory_utilization,omitempty"`
	RequestCost       map[string]float64     `protobuf:"bytes,3,rep,name=request_cost,json=requestCost,proto3" json:"request_cost,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	Utilization       map[string]float64     `protobuf:"bytes,4,rep,name=utilization,proto3" json:"utilization,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"fixed64,2,opt,name=value"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TestOrcaReport) Reset() {
	*x = TestOrcaReport{}
	mi := &file_grpc_testing_messages_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestOrcaReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestOrcaReport) ProtoMessage() {}

func (x *TestOrcaReport) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestOrcaReport.ProtoReflect.Descriptor instead.
func (*TestOrcaReport) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{19}
}

func (x *TestOrcaReport) GetCpuUtilization() float64 {
	if x != nil {
		return x.CpuUtilization
	}
	return 0
}

func (x *TestOrcaReport) GetMemoryUtilization() float64 {
	if x != nil {
		return x.MemoryUtilization
	}
	return 0
}

func (x *TestOrcaReport) GetRequestCost() map[string]float64 {
	if x != nil {
		return x.RequestCost
	}
	return nil
}

func (x *TestOrcaReport) GetUtilization() map[string]float64 {
	if x != nil {
		return x.Utilization
	}
	return nil
}

// Status that will be return to callers of the Hook method
type SetReturnStatusRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	GrpcCodeToReturn      int32                  `protobuf:"varint,1,opt,name=grpc_code_to_return,json=grpcCodeToReturn,proto3" json:"grpc_code_to_return,omitempty"`
	GrpcStatusDescription string                 `protobuf:"bytes,2,opt,name=grpc_status_description,json=grpcStatusDescription,proto3" json:"grpc_status_description,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *SetReturnStatusRequest) Reset() {
	*x = SetReturnStatusRequest{}
	mi := &file_grpc_testing_messages_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetReturnStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReturnStatusRequest) ProtoMessage() {}

func (x *SetReturnStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReturnStatusRequest.ProtoReflect.Descriptor instead.
func (*SetReturnStatusRequest) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{20}
}

func (x *SetReturnStatusRequest) GetGrpcCodeToReturn() int32 {
	if x != nil {
		return x.GrpcCodeToReturn
	}
	return 0
}

func (x *SetReturnStatusRequest) GetGrpcStatusDescription() string {
	if x != nil {
		return x.GrpcStatusDescription
	}
	return ""
}

type HookRequest struct {
	state                 protoimpl.MessageState         `protogen:"open.v1"`
	Command               HookRequest_HookRequestCommand `protobuf:"varint,1,opt,name=command,proto3,enum=grpc.testing.HookRequest_HookRequestCommand" json:"command,omitempty"`
	GrpcCodeToReturn      int32                          `protobuf:"varint,2,opt,name=grpc_code_to_return,json=grpcCodeToReturn,proto3" json:"grpc_code_to_return,omitempty"`
	GrpcStatusDescription string                         `protobuf:"bytes,3,opt,name=grpc_status_description,json=grpcStatusDescription,proto3" json:"grpc_status_description,omitempty"`
	// Server port to listen to
	ServerPort    int32 `protobuf:"varint,4,opt,name=server_port,json=serverPort,proto3" json:"server_port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HookRequest) Reset() {
	*x = HookRequest{}
	mi := &file_grpc_testing_messages_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HookRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HookRequest) ProtoMessage() {}

func (x *HookRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HookRequest.ProtoReflect.Descriptor instead.
func (*HookRequest) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{21}
}

func (x *HookRequest) GetCommand() HookRequest_HookRequestCommand {
	if x != nil {
		return x.Command
	}
	return HookRequest_UNSPECIFIED
}

func (x *HookRequest) GetGrpcCodeToReturn() int32 {
	if x != nil {
		return x.GrpcCodeToReturn
	}
	return 0
}

func (x *HookRequest) GetGrpcStatusDescription() string {
	if x != nil {
		return x.GrpcStatusDescription
	}
	return ""
}

func (x *HookRequest) GetServerPort() int32 {
	if x != nil {
		return x.ServerPort
	}
	return 0
}

type HookResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HookResponse) Reset() {
	*x = HookResponse{}
	mi := &file_grpc_testing_messages_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HookResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HookResponse) ProtoMessage() {}

func (x *HookResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HookResponse.ProtoReflect.Descriptor instead.
func (*HookResponse) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{22}
}

type LoadBalancerStatsResponse_MetadataEntry struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Key, exactly as received from the server. Case may be different from what
	// was requested in the LoadBalancerStatsRequest)
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// Value, exactly as received from the server.
	Value string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// Metadata type
	Type          LoadBalancerStatsResponse_MetadataType `protobuf:"varint,3,opt,name=type,proto3,enum=grpc.testing.LoadBalancerStatsResponse_MetadataType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadBalancerStatsResponse_MetadataEntry) Reset() {
	*x = LoadBalancerStatsResponse_MetadataEntry{}
	mi := &file_grpc_testing_messages_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadBalancerStatsResponse_MetadataEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerStatsResponse_MetadataEntry) ProtoMessage() {}

func (x *LoadBalancerStatsResponse_MetadataEntry) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerStatsResponse_MetadataEntry.ProtoReflect.Descriptor instead.
func (*LoadBalancerStatsResponse_MetadataEntry) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{13, 0}
}

func (x *LoadBalancerStatsResponse_MetadataEntry) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *LoadBalancerStatsResponse_MetadataEntry) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *LoadBalancerStatsResponse_MetadataEntry) GetType() LoadBalancerStatsResponse_MetadataType {
	if x != nil {
		return x.Type
	}
	return LoadBalancerStatsResponse_UNKNOWN
}

type LoadBalancerStatsResponse_RpcMetadata struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// metadata values for each rpc for the keys specified in
	// LoadBalancerStatsRequest.metadata_keys.
	Metadata      []*LoadBalancerStatsResponse_MetadataEntry `protobuf:"bytes,1,rep,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadBalancerStatsResponse_RpcMetadata) Reset() {
	*x = LoadBalancerStatsResponse_RpcMetadata{}
	mi := &file_grpc_testing_messages_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadBalancerStatsResponse_RpcMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerStatsResponse_RpcMetadata) ProtoMessage() {}

func (x *LoadBalancerStatsResponse_RpcMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerStatsResponse_RpcMetadata.ProtoReflect.Descriptor instead.
func (*LoadBalancerStatsResponse_RpcMetadata) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{13, 1}
}

func (x *LoadBalancerStatsResponse_RpcMetadata) GetMetadata() []*LoadBalancerStatsResponse_MetadataEntry {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type LoadBalancerStatsResponse_MetadataByPeer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// List of RpcMetadata in for each RPC with a given peer
	RpcMetadata   []*LoadBalancerStatsResponse_RpcMetadata `protobuf:"bytes,1,rep,name=rpc_metadata,json=rpcMetadata,proto3" json:"rpc_metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadBalancerStatsResponse_MetadataByPeer) Reset() {
	*x = LoadBalancerStatsResponse_MetadataByPeer{}
	mi := &file_grpc_testing_messages_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadBalancerStatsResponse_MetadataByPeer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerStatsResponse_MetadataByPeer) ProtoMessage() {}

func (x *LoadBalancerStatsResponse_MetadataByPeer) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerStatsResponse_MetadataByPeer.ProtoReflect.Descriptor instead.
func (*LoadBalancerStatsResponse_MetadataByPeer) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{13, 2}
}

func (x *LoadBalancerStatsResponse_MetadataByPeer) GetRpcMetadata() []*LoadBalancerStatsResponse_RpcMetadata {
	if x != nil {
		return x.RpcMetadata
	}
	return nil
}

type LoadBalancerStatsResponse_RpcsByPeer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The number of completed RPCs for each peer.
	RpcsByPeer    map[string]int32 `protobuf:"bytes,1,rep,name=rpcs_by_peer,json=rpcsByPeer,proto3" json:"rpcs_by_peer,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadBalancerStatsResponse_RpcsByPeer) Reset() {
	*x = LoadBalancerStatsResponse_RpcsByPeer{}
	mi := &file_grpc_testing_messages_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadBalancerStatsResponse_RpcsByPeer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerStatsResponse_RpcsByPeer) ProtoMessage() {}

func (x *LoadBalancerStatsResponse_RpcsByPeer) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerStatsResponse_RpcsByPeer.ProtoReflect.Descriptor instead.
func (*LoadBalancerStatsResponse_RpcsByPeer) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{13, 3}
}

func (x *LoadBalancerStatsResponse_RpcsByPeer) GetRpcsByPeer() map[string]int32 {
	if x != nil {
		return x.RpcsByPeer
	}
	return nil
}

type LoadBalancerAccumulatedStatsResponse_MethodStats struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The number of RPCs that were started for this method.
	RpcsStarted int32 `protobuf:"varint,1,opt,name=rpcs_started,json=rpcsStarted,proto3" json:"rpcs_started,omitempty"`
	// The number of RPCs that completed with each status for this method.  The
	// key is the integral value of a google.rpc.Code; the value is the count.
	Result        map[int32]int32 `protobuf:"bytes,2,rep,name=result,proto3" json:"result,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadBalancerAccumulatedStatsResponse_MethodStats) Reset() {
	*x = LoadBalancerAccumulatedStatsResponse_MethodStats{}
	mi := &file_grpc_testing_messages_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadBalancerAccumulatedStatsResponse_MethodStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalancerAccumulatedStatsResponse_MethodStats) ProtoMessage() {}

func (x *LoadBalancerAccumulatedStatsResponse_MethodStats) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalancerAccumulatedStatsResponse_MethodStats.ProtoReflect.Descriptor instead.
func (*LoadBalancerAccumulatedStatsResponse_MethodStats) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{15, 3}
}

func (x *LoadBalancerAccumulatedStatsResponse_MethodStats) GetRpcsStarted() int32 {
	if x != nil {
		return x.RpcsStarted
	}
	return 0
}

func (x *LoadBalancerAccumulatedStatsResponse_MethodStats) GetResult() map[int32]int32 {
	if x != nil {
		return x.Result
	}
	return nil
}

// Metadata to be attached for the given type of RPCs.
type ClientConfigureRequest_Metadata struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Type          ClientConfigureRequest_RpcType `protobuf:"varint,1,opt,name=type,proto3,enum=grpc.testing.ClientConfigureRequest_RpcType" json:"type,omitempty"`
	Key           string                         `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Value         string                         `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientConfigureRequest_Metadata) Reset() {
	*x = ClientConfigureRequest_Metadata{}
	mi := &file_grpc_testing_messages_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientConfigureRequest_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientConfigureRequest_Metadata) ProtoMessage() {}

func (x *ClientConfigureRequest_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_messages_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientConfigureRequest_Metadata.ProtoReflect.Descriptor instead.
func (*ClientConfigureRequest_Metadata) Descriptor() ([]byte, []int) {
	return file_grpc_testing_messages_proto_rawDescGZIP(), []int{16, 0}
}

func (x *ClientConfigureRequest_Metadata) GetType() ClientConfigureRequest_RpcType {
	if x != nil {
		return x.Type
	}
	return ClientConfigureRequest_EMPTY_CALL
}

func (x *ClientConfigureRequest_Metadata) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ClientConfigureRequest_Metadata) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_grpc_testing_messages_proto protoreflect.FileDescriptor

const file_grpc_testing_messages_proto_rawDesc = "" +
	"\n" +
	"\x1bgrpc/testing/messages.proto\x12\fgrpc.testing\"!\n" +
	"\tBoolValue\x12\x14\n" +
	"\x05value\x18\x01 \x01(\bR\x05value\"L\n" +
	"\aPayload\x12-\n" +
	"\x04type\x18\x01 \x01(\x0e2\x19.grpc.testing.PayloadTypeR\x04type\x12\x12\n" +
	"\x04body\x18\x02 \x01(\fR\x04body\":\n" +
	"\n" +
	"EchoStatus\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xf3\x04\n" +
	"\rSimpleRequest\x12>\n" +
	"\rresponse_type\x18\x01 \x01(\x0e2\x19.grpc.testing.PayloadTypeR\fresponseType\x12#\n" +
	"\rresponse_size\x18\x02 \x01(\x05R\fresponseSize\x12/\n" +
	"\apayload\x18\x03 \x01(\v2\x15.grpc.testing.PayloadR\apayload\x12#\n" +
	"\rfill_username\x18\x04 \x01(\bR\ffillUsername\x12(\n" +
	"\x10fill_oauth_scope\x18\x05 \x01(\bR\x0efillOauthScope\x12H\n" +
	"\x13response_compressed\x18\x06 \x01(\v2\x17.grpc.testing.BoolValueR\x12responseCompressed\x12A\n" +
	"\x0fresponse_status\x18\a \x01(\v2\x18.grpc.testing.EchoStatusR\x0eresponseStatus\x12D\n" +
	"\x11expect_compressed\x18\b \x01(\v2\x17.grpc.testing.BoolValueR\x10expectCompressed\x12$\n" +
	"\x0efill_server_id\x18\t \x01(\bR\ffillServerId\x123\n" +
	"\x16fill_grpclb_route_type\x18\n" +
	" \x01(\bR\x13fillGrpclbRouteType\x12O\n" +
	"\x15orca_per_query_report\x18\v \x01(\v2\x1c.grpc.testing.TestOrcaReportR\x12orcaPerQueryReport\"\x82\x02\n" +
	"\x0eSimpleResponse\x12/\n" +
	"\apayload\x18\x01 \x01(\v2\x15.grpc.testing.PayloadR\apayload\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1f\n" +
	"\voauth_scope\x18\x03 \x01(\tR\n" +
	"oauthScope\x12\x1b\n" +
	"\tserver_id\x18\x04 \x01(\tR\bserverId\x12I\n" +
	"\x11grpclb_route_type\x18\x05 \x01(\x0e2\x1d.grpc.testing.GrpclbRouteTypeR\x0fgrpclbRouteType\x12\x1a\n" +
	"\bhostname\x18\x06 \x01(\tR\bhostname\"\x92\x01\n" +
	"\x19StreamingInputCallRequest\x12/\n" +
	"\apayload\x18\x01 \x01(\v2\x15.grpc.testing.PayloadR\apayload\x12D\n" +
	"\x11expect_compressed\x18\x02 \x01(\v2\x17.grpc.testing.BoolValueR\x10expectCompressed\"T\n" +
	"\x1aStreamingInputCallResponse\x126\n" +
	"\x17aggregated_payload_size\x18\x01 \x01(\x05R\x15aggregatedPayloadSize\"\x82\x01\n" +
	"\x12ResponseParameters\x12\x12\n" +
	"\x04size\x18\x01 \x01(\x05R\x04size\x12\x1f\n" +
	"\vinterval_us\x18\x02 \x01(\x05R\n" +
	"intervalUs\x127\n" +
	"\n" +
	"compressed\x18\x03 \x01(\v2\x17.grpc.testing.BoolValueR\n" +
	"compressed\"\xe9\x02\n" +
	"\x1aStreamingOutputCallRequest\x12>\n" +
	"\rresponse_type\x18\x01 \x01(\x0e2\x19.grpc.testing.PayloadTypeR\fresponseType\x12Q\n" +
	"\x13response_parameters\x18\x02 \x03(\v2 .grpc.testing.ResponseParametersR\x12responseParameters\x12/\n" +
	"\apayload\x18\x03 \x01(\v2\x15.grpc.testing.PayloadR\apayload\x12A\n" +
	"\x0fresponse_status\x18\a \x01(\v2\x18.grpc.testing.EchoStatusR\x0eresponseStatus\x12D\n" +
	"\x0forca_oob_report\x18\b \x01(\v2\x1c.grpc.testing.TestOrcaReportR\rorcaOobReport\"N\n" +
	"\x1bStreamingOutputCallResponse\x12/\n" +
	"\apayload\x18\x01 \x01(\v2\x15.grpc.testing.PayloadR\apayload\"J\n" +
	"\x0fReconnectParams\x127\n" +
	"\x18max_reconnect_backoff_ms\x18\x01 \x01(\x05R\x15maxReconnectBackoffMs\"F\n" +
	"\rReconnectInfo\x12\x16\n" +
	"\x06passed\x18\x01 \x01(\bR\x06passed\x12\x1d\n" +
	"\n" +
	"backoff_ms\x18\x02 \x03(\x05R\tbackoffMs\"{\n" +
	"\x18LoadBalancerStatsRequest\x12\x19\n" +
	"\bnum_rpcs\x18\x01 \x01(\x05R\anumRpcs\x12\x1f\n" +
	"\vtimeout_sec\x18\x02 \x01(\x05R\n" +
	"timeoutSec\x12#\n" +
	"\rmetadata_keys\x18\x03 \x03(\tR\fmetadataKeys\"\xd0\t\n" +
	"\x19LoadBalancerStatsResponse\x12Y\n" +
	"\frpcs_by_peer\x18\x01 \x03(\v27.grpc.testing.LoadBalancerStatsResponse.RpcsByPeerEntryR\n" +
	"rpcsByPeer\x12!\n" +
	"\fnum_failures\x18\x02 \x01(\x05R\vnumFailures\x12_\n" +
	"\x0erpcs_by_method\x18\x03 \x03(\v29.grpc.testing.LoadBalancerStatsResponse.RpcsByMethodEntryR\frpcsByMethod\x12h\n" +
	"\x11metadatas_by_peer\x18\x04 \x03(\v2<.grpc.testing.LoadBalancerStatsResponse.MetadatasByPeerEntryR\x0fmetadatasByPeer\x1a\x81\x01\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value\x12H\n" +
	"\x04type\x18\x03 \x01(\x0e24.grpc.testing.LoadBalancerStatsResponse.MetadataTypeR\x04type\x1a`\n" +
	"\vRpcMetadata\x12Q\n" +
	"\bmetadata\x18\x01 \x03(\v25.grpc.testing.LoadBalancerStatsResponse.MetadataEntryR\bmetadata\x1ah\n" +
	"\x0eMetadataByPeer\x12V\n" +
	"\frpc_metadata\x18\x01 \x03(\v23.grpc.testing.LoadBalancerStatsResponse.RpcMetadataR\vrpcMetadata\x1a\xb1\x01\n" +
	"\n" +
	"RpcsByPeer\x12d\n" +
	"\frpcs_by_peer\x18\x01 \x03(\v2B.grpc.testing.LoadBalancerStatsResponse.RpcsByPeer.RpcsByPeerEntryR\n" +
	"rpcsByPeer\x1a=\n" +
	"\x0fRpcsByPeerEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\x1a=\n" +
	"\x0fRpcsByPeerEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\x1as\n" +
	"\x11RpcsByMethodEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12H\n" +
	"\x05value\x18\x02 \x01(\v22.grpc.testing.LoadBalancerStatsResponse.RpcsByPeerR\x05value:\x028\x01\x1az\n" +
	"\x14MetadatasByPeerEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12L\n" +
	"\x05value\x18\x02 \x01(\v26.grpc.testing.LoadBalancerStatsResponse.MetadataByPeerR\x05value:\x028\x01\"6\n" +
	"\fMetadataType\x12\v\n" +
	"\aUNKNOWN\x10\x00\x12\v\n" +
	"\aINITIAL\x10\x01\x12\f\n" +
	"\bTRAILING\x10\x02\"%\n" +
	"#LoadBalancerAccumulatedStatsRequest\"\x86\t\n" +
	"$LoadBalancerAccumulatedStatsResponse\x12\x8e\x01\n" +
	"\x1anum_rpcs_started_by_method\x18\x01 \x03(\v2N.grpc.testing.LoadBalancerAccumulatedStatsResponse.NumRpcsStartedByMethodEntryB\x02\x18\x01R\x16numRpcsStartedByMethod\x12\x94\x01\n" +
	"\x1cnum_rpcs_succeeded_by_method\x18\x02 \x03(\v2P.grpc.testing.LoadBalancerAccumulatedStatsResponse.NumRpcsSucceededByMethodEntryB\x02\x18\x01R\x18numRpcsSucceededByMethod\x12\x8b\x01\n" +
	"\x19num_rpcs_failed_by_method\x18\x03 \x03(\v2M.grpc.testing.LoadBalancerAccumulatedStatsResponse.NumRpcsFailedByMethodEntryB\x02\x18\x01R\x15numRpcsFailedByMethod\x12p\n" +
	"\x10stats_per_method\x18\x04 \x03(\v2F.grpc.testing.LoadBalancerAccumulatedStatsResponse.StatsPerMethodEntryR\x0estatsPerMethod\x1aI\n" +
	"\x1bNumRpcsStartedByMethodEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\x1aK\n" +
	"\x1dNumRpcsSucceededByMethodEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\x1aH\n" +
	"\x1aNumRpcsFailedByMethodEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\x1a\xcf\x01\n" +
	"\vMethodStats\x12!\n" +
	"\frpcs_started\x18\x01 \x01(\x05R\vrpcsStarted\x12b\n" +
	"\x06result\x18\x02 \x03(\v2J.grpc.testing.LoadBalancerAccumulatedStatsResponse.MethodStats.ResultEntryR\x06result\x1a9\n" +
	"\vResultEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\x05R\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\x1a\x81\x01\n" +
	"\x13StatsPerMethodEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12T\n" +
	"\x05value\x18\x02 \x01(\v2>.grpc.testing.LoadBalancerAccumulatedStatsResponse.MethodStatsR\x05value:\x028\x01\"\xe9\x02\n" +
	"\x16ClientConfigureRequest\x12B\n" +
	"\x05types\x18\x01 \x03(\x0e2,.grpc.testing.ClientConfigureRequest.RpcTypeR\x05types\x12I\n" +
	"\bmetadata\x18\x02 \x03(\v2-.grpc.testing.ClientConfigureRequest.MetadataR\bmetadata\x12\x1f\n" +
	"\vtimeout_sec\x18\x03 \x01(\x05R\n" +
	"timeoutSec\x1at\n" +
	"\bMetadata\x12@\n" +
	"\x04type\x18\x01 \x01(\x0e2,.grpc.testing.ClientConfigureRequest.RpcTypeR\x04type\x12\x10\n" +
	"\x03key\x18\x02 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x03 \x01(\tR\x05value\")\n" +
	"\aRpcType\x12\x0e\n" +
	"\n" +
	"EMPTY_CALL\x10\x00\x12\x0e\n" +
	"\n" +
	"UNARY_CALL\x10\x01\"\x19\n" +
	"\x17ClientConfigureResponse\"\x1e\n" +
	"\n" +
	"MemorySize\x12\x10\n" +
	"\x03rss\x18\x01 \x01(\x03R\x03rss\"\x8b\x03\n" +
	"\x0eTestOrcaReport\x12'\n" +
	"\x0fcpu_utilization\x18\x01 \x01(\x01R\x0ecpuUtilization\x12-\n" +
	"\x12memory_utilization\x18\x02 \x01(\x01R\x11memoryUtilization\x12P\n" +
	"\frequest_cost\x18\x03 \x03(\v2-.grpc.testing.TestOrcaReport.RequestCostEntryR\vrequestCost\x12O\n" +
	"\vutilization\x18\x04 \x03(\v2-.grpc.testing.TestOrcaReport.UtilizationEntryR\vutilization\x1a>\n" +
	"\x10RequestCostEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x01R\x05value:\x028\x01\x1a>\n" +
	"\x10UtilizationEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x01R\x05value:\x028\x01\"\x7f\n" +
	"\x16SetReturnStatusRequest\x12-\n" +
	"\x13grpc_code_to_return\x18\x01 \x01(\x05R\x10grpcCodeToReturn\x126\n" +
	"\x17grpc_status_description\x18\x02 \x01(\tR\x15grpcStatusDescription\"\xa5\x02\n" +
	"\vHookRequest\x12F\n" +
	"\acommand\x18\x01 \x01(\x0e2,.grpc.testing.HookRequest.HookRequestCommandR\acommand\x12-\n" +
	"\x13grpc_code_to_return\x18\x02 \x01(\x05R\x10grpcCodeToReturn\x126\n" +
	"\x17grpc_status_description\x18\x03 \x01(\tR\x15grpcStatusDescription\x12\x1f\n" +
	"\vserver_port\x18\x04 \x01(\x05R\n" +
	"serverPort\"F\n" +
	"\x12HookRequestCommand\x12\x0f\n" +
	"\vUNSPECIFIED\x10\x00\x12\t\n" +
	"\x05START\x10\x01\x12\b\n" +
	"\x04STOP\x10\x02\x12\n" +
	"\n" +
	"\x06RETURN\x10\x03\"\x0e\n" +
	"\fHookResponse*\x1f\n" +
	"\vPayloadType\x12\x10\n" +
	"\fCOMPRESSABLE\x10\x00*o\n" +
	"\x0fGrpclbRouteType\x12\x1d\n" +
	"\x19GRPCLB_ROUTE_TYPE_UNKNOWN\x10\x00\x12\x1e\n" +
	"\x1aGRPCLB_ROUTE_TYPE_FALLBACK\x10\x01\x12\x1d\n" +
	"\x19GRPCLB_ROUTE_TYPE_BACKEND\x10\x02B\x1d\n" +
	"\x1bio.grpc.testing.integrationb\x06proto3"

var (
	file_grpc_testing_messages_proto_rawDescOnce sync.Once
	file_grpc_testing_messages_proto_rawDescData []byte
)

func file_grpc_testing_messages_proto_rawDescGZIP() []byte {
	file_grpc_testing_messages_proto_rawDescOnce.Do(func() {
		file_grpc_testing_messages_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_grpc_testing_messages_proto_rawDesc), len(file_grpc_testing_messages_proto_rawDesc)))
	})
	return file_grpc_testing_messages_proto_rawDescData
}

var file_grpc_testing_messages_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_grpc_testing_messages_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_grpc_testing_messages_proto_goTypes = []any{
	(PayloadType)(0),                                 // 0: grpc.testing.PayloadType
	(GrpclbRouteType)(0),                             // 1: grpc.testing.GrpclbRouteType
	(LoadBalancerStatsResponse_MetadataType)(0),      // 2: grpc.testing.LoadBalancerStatsResponse.MetadataType
	(ClientConfigureRequest_RpcType)(0),              // 3: grpc.testing.ClientConfigureRequest.RpcType
	(HookRequest_HookRequestCommand)(0),              // 4: grpc.testing.HookRequest.HookRequestCommand
	(*BoolValue)(nil),                                // 5: grpc.testing.BoolValue
	(*Payload)(nil),                                  // 6: grpc.testing.Payload
	(*EchoStatus)(nil),                               // 7: grpc.testing.EchoStatus
	(*SimpleRequest)(nil),                            // 8: grpc.testing.SimpleRequest
	(*SimpleResponse)(nil),                           // 9: grpc.testing.SimpleResponse
	(*StreamingInputCallRequest)(nil),                // 10: grpc.testing.StreamingInputCallRequest
	(*StreamingInputCallResponse)(nil),               // 11: grpc.testing.StreamingInputCallResponse
	(*ResponseParameters)(nil),                       // 12: grpc.testing.ResponseParameters
	(*StreamingOutputCallRequest)(nil),               // 13: grpc.testing.StreamingOutputCallRequest
	(*StreamingOutputCallResponse)(nil),              // 14: grpc.testing.StreamingOutputCallResponse
	(*ReconnectParams)(nil),                          // 15: grpc.testing.ReconnectParams
	(*ReconnectInfo)(nil),                            // 16: grpc.testing.ReconnectInfo
	(*LoadBalancerStatsRequest)(nil),                 // 17: grpc.testing.LoadBalancerStatsRequest
	(*LoadBalancerStatsResponse)(nil),                // 18: grpc.testing.LoadBalancerStatsResponse
	(*LoadBalancerAccumulatedStatsRequest)(nil),      // 19: grpc.testing.LoadBalancerAccumulatedStatsRequest
	(*LoadBalancerAccumulatedStatsResponse)(nil),     // 20: grpc.testing.LoadBalancerAccumulatedStatsResponse
	(*ClientConfigureRequest)(nil),                   // 21: grpc.testing.ClientConfigureRequest
	(*ClientConfigureResponse)(nil),                  // 22: grpc.testing.ClientConfigureResponse
	(*MemorySize)(nil),                               // 23: grpc.testing.MemorySize
	(*TestOrcaReport)(nil),                           // 24: grpc.testing.TestOrcaReport
	(*SetReturnStatusRequest)(nil),                   // 25: grpc.testing.SetReturnStatusRequest
	(*HookRequest)(nil),                              // 26: grpc.testing.HookRequest
	(*HookResponse)(nil),                             // 27: grpc.testing.HookResponse
	(*LoadBalancerStatsResponse_MetadataEntry)(nil),  // 28: grpc.testing.LoadBalancerStatsResponse.MetadataEntry
	(*LoadBalancerStatsResponse_RpcMetadata)(nil),    // 29: grpc.testing.LoadBalancerStatsResponse.RpcMetadata
	(*LoadBalancerStatsResponse_MetadataByPeer)(nil), // 30: grpc.testing.LoadBalancerStatsResponse.MetadataByPeer
	(*LoadBalancerStatsResponse_RpcsByPeer)(nil),     // 31: grpc.testing.LoadBalancerStatsResponse.RpcsByPeer
	nil, // 32: grpc.testing.LoadBalancerStatsResponse.RpcsByPeerEntry
	nil, // 33: grpc.testing.LoadBalancerStatsResponse.RpcsByMethodEntry
	nil, // 34: grpc.testing.LoadBalancerStatsResponse.MetadatasByPeerEntry
	nil, // 35: grpc.testing.LoadBalancerStatsResponse.RpcsByPeer.RpcsByPeerEntry
	nil, // 36: grpc.testing.LoadBalancerAccumulatedStatsResponse.NumRpcsStartedByMethodEntry
	nil, // 37: grpc.testing.LoadBalancerAccumulatedStatsResponse.NumRpcsSucceededByMethodEntry
	nil, // 38: grpc.testing.LoadBalancerAccumulatedStatsResponse.NumRpcsFailedByMethodEntry
	(*LoadBalancerAccumulatedStatsResponse_MethodStats)(nil), // 39: grpc.testing.LoadBalancerAccumulatedStatsResponse.MethodStats
	nil,                                     // 40: grpc.testing.LoadBalancerAccumulatedStatsResponse.StatsPerMethodEntry
	nil,                                     // 41: grpc.testing.LoadBalancerAccumulatedStatsResponse.MethodStats.ResultEntry
	(*ClientConfigureRequest_Metadata)(nil), // 42: grpc.testing.ClientConfigureRequest.Metadata
	nil,                                     // 43: grpc.testing.TestOrcaReport.RequestCostEntry
	nil,                                     // 44: grpc.testing.TestOrcaReport.UtilizationEntry
}
var file_grpc_testing_messages_proto_depIdxs = []int32{
	0,  // 0: grpc.testing.Payload.type:type_name -> grpc.testing.PayloadType
	0,  // 1: grpc.testing.SimpleRequest.response_type:type_name -> grpc.testing.PayloadType
	6,  // 2: grpc.testing.SimpleRequest.payload:type_name -> grpc.testing.Payload
	5,  // 3: grpc.testing.SimpleRequest.response_compressed:type_name -> grpc.testing.BoolValue
	7,  // 4: grpc.testing.SimpleRequest.response_status:type_name -> grpc.testing.EchoStatus
	5,  // 5: grpc.testing.SimpleRequest.expect_compressed:type_name -> grpc.testing.BoolValue
	24, // 6: grpc.testing.SimpleRequest.orca_per_query_report:type_name -> grpc.testing.TestOrcaReport
	6,  // 7: grpc.testing.SimpleResponse.payload:type_name -> grpc.testing.Payload
	1,  // 8: grpc.testing.SimpleResponse.grpclb_route_type:type_name -> grpc.testing.GrpclbRouteType
	6,  // 9: grpc.testing.StreamingInputCallRequest.payload:type_name -> grpc.testing.Payload
	5,  // 10: grpc.testing.StreamingInputCallRequest.expect_compressed:type_name -> grpc.testing.BoolValue
	5,  // 11: grpc.testing.ResponseParameters.compressed:type_name -> grpc.testing.BoolValue
	0,  // 12: grpc.testing.StreamingOutputCallRequest.response_type:type_name -> grpc.testing.PayloadType
	12, // 13: grpc.testing.StreamingOutputCallRequest.response_parameters:type_name -> grpc.testing.ResponseParameters
	6,  // 14: grpc.testing.StreamingOutputCallRequest.payload:type_name -> grpc.testing.Payload
	7,  // 15: grpc.testing.StreamingOutputCallRequest.response_status:type_name -> grpc.testing.EchoStatus
	24, // 16: grpc.testing.StreamingOutputCallRequest.orca_oob_report:type_name -> grpc.testing.TestOrcaReport
	6,  // 17: grpc.testing.StreamingOutputCallResponse.payload:type_name -> grpc.testing.Payload
	32, // 18: grpc.testing.LoadBalancerStatsResponse.rpcs_by_peer:type_name -> grpc.testing.LoadBalancerStatsResponse.RpcsByPeerEntry
	33, // 19: grpc.testing.LoadBalancerStatsResponse.rpcs_by_method:type_name -> grpc.testing.LoadBalancerStatsResponse.RpcsByMethodEntry
	34, // 20: grpc.testing.LoadBalancerStatsResponse.metadatas_by_peer:type_name -> grpc.testing.LoadBalancerStatsResponse.MetadatasByPeerEntry
	36, // 21: grpc.testing.LoadBalancerAccumulatedStatsResponse.num_rpcs_started_by_method:type_name -> grpc.testing.LoadBalancerAccumulatedStatsResponse.NumRpcsStartedByMethodEntry
	37, // 22: grpc.testing.LoadBalancerAccumulatedStatsResponse.num_rpcs_succeeded_by_method:type_name -> grpc.testing.LoadBalancerAccumulatedStatsResponse.NumRpcsSucceededByMethodEntry
	38, // 23: grpc.testing.LoadBalancerAccumulatedStatsResponse.num_rpcs_failed_by_method:type_name -> grpc.testing.LoadBalancerAccumulatedStatsResponse.NumRpcsFailedByMethodEntry
	40, // 24: grpc.testing.LoadBalancerAccumulatedStatsResponse.stats_per_method:type_name -> grpc.testing.LoadBalancerAccumulatedStatsResponse.StatsPerMethodEntry
	3,  // 25: grpc.testing.ClientConfigureRequest.types:type_name -> grpc.testing.ClientConfigureRequest.RpcType
	42, // 26: grpc.testing.ClientConfigureRequest.metadata:type_name -> grpc.testing.ClientConfigureRequest.Metadata
	43, // 27: grpc.testing.TestOrcaReport.request_cost:type_name -> grpc.testing.TestOrcaReport.RequestCostEntry
	44, // 28: grpc.testing.TestOrcaReport.utilization:type_name -> grpc.testing.TestOrcaReport.UtilizationEntry
	4,  // 29: grpc.testing.HookRequest.command:type_name -> grpc.testing.HookRequest.HookRequestCommand
	2,  // 30: grpc.testing.LoadBalancerStatsResponse.MetadataEntry.type:type_name -> grpc.testing.LoadBalancerStatsResponse.MetadataType
	28, // 31: grpc.testing.LoadBalancerStatsResponse.RpcMetadata.metadata:type_name -> grpc.testing.LoadBalancerStatsResponse.MetadataEntry
	29, // 32: grpc.testing.LoadBalancerStatsResponse.MetadataByPeer.rpc_metadata:type_name -> grpc.testing.LoadBalancerStatsResponse.RpcMetadata
	35, // 33: grpc.testing.LoadBalancerStatsResponse.RpcsByPeer.rpcs_by_peer:type_name -> grpc.testing.LoadBalancerStatsResponse.RpcsByPeer.RpcsByPeerEntry
	31, // 34: grpc.testing.LoadBalancerStatsResponse.RpcsByMethodEntry.value:type_name -> grpc.testing.LoadBalancerStatsResponse.RpcsByPeer
	30, // 35: grpc.testing.LoadBalancerStatsResponse.MetadatasByPeerEntry.value:type_name -> grpc.testing.LoadBalancerStatsResponse.MetadataByPeer
	41, // 36: grpc.testing.LoadBalancerAccumulatedStatsResponse.MethodStats.result:type_name -> grpc.testing.LoadBalancerAccumulatedStatsResponse.MethodStats.ResultEntry
	39, // 37: grpc.testing.LoadBalancerAccumulatedStatsResponse.StatsPerMethodEntry.value:type_name -> grpc.testing.LoadBalancerAccumulatedStatsResponse.MethodStats
	3,  // 38: grpc.testing.ClientConfigureRequest.Metadata.type:type_name -> grpc.testing.ClientConfigureRequest.RpcType
	39, // [39:39] is the sub-list for method output_type
	39, // [39:39] is the sub-list for method input_type
	39, // [39:39] is the sub-list for extension type_name
	39, // [39:39] is the sub-list for extension extendee
	0,  // [0:39] is the sub-list for field type_name
}

func init() { file_grpc_testing_messages_proto_init() }
func file_grpc_testing_messages_proto_init() {
	if File_grpc_testing_messages_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_grpc_testing_messages_proto_rawDesc), len(file_grpc_testing_messages_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   40,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_grpc_testing_messages_proto_goTypes,
		DependencyIndexes: file_grpc_testing_messages_proto_depIdxs,
		EnumInfos:         file_grpc_testing_messages_proto_enumTypes,
		MessageInfos:      file_grpc_testing_messages_proto_msgTypes,
	}.Build()
	File_grpc_testing_messages_proto = out.File
	file_grpc_testing_messages_proto_goTypes = nil
	file_grpc_testing_messages_proto_depIdxs = nil
}
