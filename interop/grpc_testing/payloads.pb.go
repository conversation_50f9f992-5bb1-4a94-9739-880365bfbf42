// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: grpc/testing/payloads.proto

package grpc_testing

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ByteBufferParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReqSize       int32                  `protobuf:"varint,1,opt,name=req_size,json=reqSize,proto3" json:"req_size,omitempty"`
	RespSize      int32                  `protobuf:"varint,2,opt,name=resp_size,json=respSize,proto3" json:"resp_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ByteBufferParams) Reset() {
	*x = ByteBufferParams{}
	mi := &file_grpc_testing_payloads_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ByteBufferParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ByteBufferParams) ProtoMessage() {}

func (x *ByteBufferParams) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_payloads_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ByteBufferParams.ProtoReflect.Descriptor instead.
func (*ByteBufferParams) Descriptor() ([]byte, []int) {
	return file_grpc_testing_payloads_proto_rawDescGZIP(), []int{0}
}

func (x *ByteBufferParams) GetReqSize() int32 {
	if x != nil {
		return x.ReqSize
	}
	return 0
}

func (x *ByteBufferParams) GetRespSize() int32 {
	if x != nil {
		return x.RespSize
	}
	return 0
}

type SimpleProtoParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReqSize       int32                  `protobuf:"varint,1,opt,name=req_size,json=reqSize,proto3" json:"req_size,omitempty"`
	RespSize      int32                  `protobuf:"varint,2,opt,name=resp_size,json=respSize,proto3" json:"resp_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SimpleProtoParams) Reset() {
	*x = SimpleProtoParams{}
	mi := &file_grpc_testing_payloads_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimpleProtoParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimpleProtoParams) ProtoMessage() {}

func (x *SimpleProtoParams) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_payloads_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimpleProtoParams.ProtoReflect.Descriptor instead.
func (*SimpleProtoParams) Descriptor() ([]byte, []int) {
	return file_grpc_testing_payloads_proto_rawDescGZIP(), []int{1}
}

func (x *SimpleProtoParams) GetReqSize() int32 {
	if x != nil {
		return x.ReqSize
	}
	return 0
}

func (x *SimpleProtoParams) GetRespSize() int32 {
	if x != nil {
		return x.RespSize
	}
	return 0
}

type ComplexProtoParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ComplexProtoParams) Reset() {
	*x = ComplexProtoParams{}
	mi := &file_grpc_testing_payloads_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ComplexProtoParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComplexProtoParams) ProtoMessage() {}

func (x *ComplexProtoParams) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_payloads_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComplexProtoParams.ProtoReflect.Descriptor instead.
func (*ComplexProtoParams) Descriptor() ([]byte, []int) {
	return file_grpc_testing_payloads_proto_rawDescGZIP(), []int{2}
}

type PayloadConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Payload:
	//
	//	*PayloadConfig_BytebufParams
	//	*PayloadConfig_SimpleParams
	//	*PayloadConfig_ComplexParams
	Payload       isPayloadConfig_Payload `protobuf_oneof:"payload"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PayloadConfig) Reset() {
	*x = PayloadConfig{}
	mi := &file_grpc_testing_payloads_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PayloadConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PayloadConfig) ProtoMessage() {}

func (x *PayloadConfig) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_payloads_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PayloadConfig.ProtoReflect.Descriptor instead.
func (*PayloadConfig) Descriptor() ([]byte, []int) {
	return file_grpc_testing_payloads_proto_rawDescGZIP(), []int{3}
}

func (x *PayloadConfig) GetPayload() isPayloadConfig_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *PayloadConfig) GetBytebufParams() *ByteBufferParams {
	if x != nil {
		if x, ok := x.Payload.(*PayloadConfig_BytebufParams); ok {
			return x.BytebufParams
		}
	}
	return nil
}

func (x *PayloadConfig) GetSimpleParams() *SimpleProtoParams {
	if x != nil {
		if x, ok := x.Payload.(*PayloadConfig_SimpleParams); ok {
			return x.SimpleParams
		}
	}
	return nil
}

func (x *PayloadConfig) GetComplexParams() *ComplexProtoParams {
	if x != nil {
		if x, ok := x.Payload.(*PayloadConfig_ComplexParams); ok {
			return x.ComplexParams
		}
	}
	return nil
}

type isPayloadConfig_Payload interface {
	isPayloadConfig_Payload()
}

type PayloadConfig_BytebufParams struct {
	BytebufParams *ByteBufferParams `protobuf:"bytes,1,opt,name=bytebuf_params,json=bytebufParams,proto3,oneof"`
}

type PayloadConfig_SimpleParams struct {
	SimpleParams *SimpleProtoParams `protobuf:"bytes,2,opt,name=simple_params,json=simpleParams,proto3,oneof"`
}

type PayloadConfig_ComplexParams struct {
	ComplexParams *ComplexProtoParams `protobuf:"bytes,3,opt,name=complex_params,json=complexParams,proto3,oneof"`
}

func (*PayloadConfig_BytebufParams) isPayloadConfig_Payload() {}

func (*PayloadConfig_SimpleParams) isPayloadConfig_Payload() {}

func (*PayloadConfig_ComplexParams) isPayloadConfig_Payload() {}

var File_grpc_testing_payloads_proto protoreflect.FileDescriptor

const file_grpc_testing_payloads_proto_rawDesc = "" +
	"\n" +
	"\x1bgrpc/testing/payloads.proto\x12\fgrpc.testing\"J\n" +
	"\x10ByteBufferParams\x12\x19\n" +
	"\breq_size\x18\x01 \x01(\x05R\areqSize\x12\x1b\n" +
	"\tresp_size\x18\x02 \x01(\x05R\brespSize\"K\n" +
	"\x11SimpleProtoParams\x12\x19\n" +
	"\breq_size\x18\x01 \x01(\x05R\areqSize\x12\x1b\n" +
	"\tresp_size\x18\x02 \x01(\x05R\brespSize\"\x14\n" +
	"\x12ComplexProtoParams\"\xf6\x01\n" +
	"\rPayloadConfig\x12G\n" +
	"\x0ebytebuf_params\x18\x01 \x01(\v2\x1e.grpc.testing.ByteBufferParamsH\x00R\rbytebufParams\x12F\n" +
	"\rsimple_params\x18\x02 \x01(\v2\x1f.grpc.testing.SimpleProtoParamsH\x00R\fsimpleParams\x12I\n" +
	"\x0ecomplex_params\x18\x03 \x01(\v2 .grpc.testing.ComplexProtoParamsH\x00R\rcomplexParamsB\t\n" +
	"\apayloadB\"\n" +
	"\x0fio.grpc.testingB\rPayloadsProtoP\x01b\x06proto3"

var (
	file_grpc_testing_payloads_proto_rawDescOnce sync.Once
	file_grpc_testing_payloads_proto_rawDescData []byte
)

func file_grpc_testing_payloads_proto_rawDescGZIP() []byte {
	file_grpc_testing_payloads_proto_rawDescOnce.Do(func() {
		file_grpc_testing_payloads_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_grpc_testing_payloads_proto_rawDesc), len(file_grpc_testing_payloads_proto_rawDesc)))
	})
	return file_grpc_testing_payloads_proto_rawDescData
}

var file_grpc_testing_payloads_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_grpc_testing_payloads_proto_goTypes = []any{
	(*ByteBufferParams)(nil),   // 0: grpc.testing.ByteBufferParams
	(*SimpleProtoParams)(nil),  // 1: grpc.testing.SimpleProtoParams
	(*ComplexProtoParams)(nil), // 2: grpc.testing.ComplexProtoParams
	(*PayloadConfig)(nil),      // 3: grpc.testing.PayloadConfig
}
var file_grpc_testing_payloads_proto_depIdxs = []int32{
	0, // 0: grpc.testing.PayloadConfig.bytebuf_params:type_name -> grpc.testing.ByteBufferParams
	1, // 1: grpc.testing.PayloadConfig.simple_params:type_name -> grpc.testing.SimpleProtoParams
	2, // 2: grpc.testing.PayloadConfig.complex_params:type_name -> grpc.testing.ComplexProtoParams
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_grpc_testing_payloads_proto_init() }
func file_grpc_testing_payloads_proto_init() {
	if File_grpc_testing_payloads_proto != nil {
		return
	}
	file_grpc_testing_payloads_proto_msgTypes[3].OneofWrappers = []any{
		(*PayloadConfig_BytebufParams)(nil),
		(*PayloadConfig_SimpleParams)(nil),
		(*PayloadConfig_ComplexParams)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_grpc_testing_payloads_proto_rawDesc), len(file_grpc_testing_payloads_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_grpc_testing_payloads_proto_goTypes,
		DependencyIndexes: file_grpc_testing_payloads_proto_depIdxs,
		MessageInfos:      file_grpc_testing_payloads_proto_msgTypes,
	}.Build()
	File_grpc_testing_payloads_proto = out.File
	file_grpc_testing_payloads_proto_goTypes = nil
	file_grpc_testing_payloads_proto_depIdxs = nil
}
