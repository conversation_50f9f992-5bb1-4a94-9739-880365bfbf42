// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: grpc/testing/stats.proto

package grpc_testing

import (
	core "google.golang.org/grpc/interop/grpc_testing/core"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServerStats struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// wall clock time change in seconds since last reset
	TimeElapsed float64 `protobuf:"fixed64,1,opt,name=time_elapsed,json=timeElapsed,proto3" json:"time_elapsed,omitempty"`
	// change in user time (in seconds) used by the server since last reset
	TimeUser float64 `protobuf:"fixed64,2,opt,name=time_user,json=timeUser,proto3" json:"time_user,omitempty"`
	// change in server time (in seconds) used by the server process and all
	// threads since last reset
	TimeSystem float64 `protobuf:"fixed64,3,opt,name=time_system,json=timeSystem,proto3" json:"time_system,omitempty"`
	// change in total cpu time of the server (data from proc/stat)
	TotalCpuTime uint64 `protobuf:"varint,4,opt,name=total_cpu_time,json=totalCpuTime,proto3" json:"total_cpu_time,omitempty"`
	// change in idle time of the server (data from proc/stat)
	IdleCpuTime uint64 `protobuf:"varint,5,opt,name=idle_cpu_time,json=idleCpuTime,proto3" json:"idle_cpu_time,omitempty"`
	// Number of polls called inside completion queue
	CqPollCount uint64 `protobuf:"varint,6,opt,name=cq_poll_count,json=cqPollCount,proto3" json:"cq_poll_count,omitempty"`
	// Core library stats
	CoreStats     *core.Stats `protobuf:"bytes,7,opt,name=core_stats,json=coreStats,proto3" json:"core_stats,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerStats) Reset() {
	*x = ServerStats{}
	mi := &file_grpc_testing_stats_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerStats) ProtoMessage() {}

func (x *ServerStats) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_stats_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerStats.ProtoReflect.Descriptor instead.
func (*ServerStats) Descriptor() ([]byte, []int) {
	return file_grpc_testing_stats_proto_rawDescGZIP(), []int{0}
}

func (x *ServerStats) GetTimeElapsed() float64 {
	if x != nil {
		return x.TimeElapsed
	}
	return 0
}

func (x *ServerStats) GetTimeUser() float64 {
	if x != nil {
		return x.TimeUser
	}
	return 0
}

func (x *ServerStats) GetTimeSystem() float64 {
	if x != nil {
		return x.TimeSystem
	}
	return 0
}

func (x *ServerStats) GetTotalCpuTime() uint64 {
	if x != nil {
		return x.TotalCpuTime
	}
	return 0
}

func (x *ServerStats) GetIdleCpuTime() uint64 {
	if x != nil {
		return x.IdleCpuTime
	}
	return 0
}

func (x *ServerStats) GetCqPollCount() uint64 {
	if x != nil {
		return x.CqPollCount
	}
	return 0
}

func (x *ServerStats) GetCoreStats() *core.Stats {
	if x != nil {
		return x.CoreStats
	}
	return nil
}

// Histogram params based on grpc/support/histogram.c
type HistogramParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Resolution    float64                `protobuf:"fixed64,1,opt,name=resolution,proto3" json:"resolution,omitempty"`                      // first bucket is [0, 1 + resolution)
	MaxPossible   float64                `protobuf:"fixed64,2,opt,name=max_possible,json=maxPossible,proto3" json:"max_possible,omitempty"` // use enough buckets to allow this value
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistogramParams) Reset() {
	*x = HistogramParams{}
	mi := &file_grpc_testing_stats_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistogramParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistogramParams) ProtoMessage() {}

func (x *HistogramParams) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_stats_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistogramParams.ProtoReflect.Descriptor instead.
func (*HistogramParams) Descriptor() ([]byte, []int) {
	return file_grpc_testing_stats_proto_rawDescGZIP(), []int{1}
}

func (x *HistogramParams) GetResolution() float64 {
	if x != nil {
		return x.Resolution
	}
	return 0
}

func (x *HistogramParams) GetMaxPossible() float64 {
	if x != nil {
		return x.MaxPossible
	}
	return 0
}

// Histogram data based on grpc/support/histogram.c
type HistogramData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bucket        []uint32               `protobuf:"varint,1,rep,packed,name=bucket,proto3" json:"bucket,omitempty"`
	MinSeen       float64                `protobuf:"fixed64,2,opt,name=min_seen,json=minSeen,proto3" json:"min_seen,omitempty"`
	MaxSeen       float64                `protobuf:"fixed64,3,opt,name=max_seen,json=maxSeen,proto3" json:"max_seen,omitempty"`
	Sum           float64                `protobuf:"fixed64,4,opt,name=sum,proto3" json:"sum,omitempty"`
	SumOfSquares  float64                `protobuf:"fixed64,5,opt,name=sum_of_squares,json=sumOfSquares,proto3" json:"sum_of_squares,omitempty"`
	Count         float64                `protobuf:"fixed64,6,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HistogramData) Reset() {
	*x = HistogramData{}
	mi := &file_grpc_testing_stats_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HistogramData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HistogramData) ProtoMessage() {}

func (x *HistogramData) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_stats_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HistogramData.ProtoReflect.Descriptor instead.
func (*HistogramData) Descriptor() ([]byte, []int) {
	return file_grpc_testing_stats_proto_rawDescGZIP(), []int{2}
}

func (x *HistogramData) GetBucket() []uint32 {
	if x != nil {
		return x.Bucket
	}
	return nil
}

func (x *HistogramData) GetMinSeen() float64 {
	if x != nil {
		return x.MinSeen
	}
	return 0
}

func (x *HistogramData) GetMaxSeen() float64 {
	if x != nil {
		return x.MaxSeen
	}
	return 0
}

func (x *HistogramData) GetSum() float64 {
	if x != nil {
		return x.Sum
	}
	return 0
}

func (x *HistogramData) GetSumOfSquares() float64 {
	if x != nil {
		return x.SumOfSquares
	}
	return 0
}

func (x *HistogramData) GetCount() float64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type RequestResultCount struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StatusCode    int32                  `protobuf:"varint,1,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
	Count         int64                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestResultCount) Reset() {
	*x = RequestResultCount{}
	mi := &file_grpc_testing_stats_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestResultCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestResultCount) ProtoMessage() {}

func (x *RequestResultCount) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_stats_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestResultCount.ProtoReflect.Descriptor instead.
func (*RequestResultCount) Descriptor() ([]byte, []int) {
	return file_grpc_testing_stats_proto_rawDescGZIP(), []int{3}
}

func (x *RequestResultCount) GetStatusCode() int32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *RequestResultCount) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ClientStats struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Latency histogram. Data points are in nanoseconds.
	Latencies *HistogramData `protobuf:"bytes,1,opt,name=latencies,proto3" json:"latencies,omitempty"`
	// See ServerStats for details.
	TimeElapsed float64 `protobuf:"fixed64,2,opt,name=time_elapsed,json=timeElapsed,proto3" json:"time_elapsed,omitempty"`
	TimeUser    float64 `protobuf:"fixed64,3,opt,name=time_user,json=timeUser,proto3" json:"time_user,omitempty"`
	TimeSystem  float64 `protobuf:"fixed64,4,opt,name=time_system,json=timeSystem,proto3" json:"time_system,omitempty"`
	// Number of failed requests (one row per status code seen)
	RequestResults []*RequestResultCount `protobuf:"bytes,5,rep,name=request_results,json=requestResults,proto3" json:"request_results,omitempty"`
	// Number of polls called inside completion queue
	CqPollCount uint64 `protobuf:"varint,6,opt,name=cq_poll_count,json=cqPollCount,proto3" json:"cq_poll_count,omitempty"`
	// Core library stats
	CoreStats     *core.Stats `protobuf:"bytes,7,opt,name=core_stats,json=coreStats,proto3" json:"core_stats,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientStats) Reset() {
	*x = ClientStats{}
	mi := &file_grpc_testing_stats_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientStats) ProtoMessage() {}

func (x *ClientStats) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_stats_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientStats.ProtoReflect.Descriptor instead.
func (*ClientStats) Descriptor() ([]byte, []int) {
	return file_grpc_testing_stats_proto_rawDescGZIP(), []int{4}
}

func (x *ClientStats) GetLatencies() *HistogramData {
	if x != nil {
		return x.Latencies
	}
	return nil
}

func (x *ClientStats) GetTimeElapsed() float64 {
	if x != nil {
		return x.TimeElapsed
	}
	return 0
}

func (x *ClientStats) GetTimeUser() float64 {
	if x != nil {
		return x.TimeUser
	}
	return 0
}

func (x *ClientStats) GetTimeSystem() float64 {
	if x != nil {
		return x.TimeSystem
	}
	return 0
}

func (x *ClientStats) GetRequestResults() []*RequestResultCount {
	if x != nil {
		return x.RequestResults
	}
	return nil
}

func (x *ClientStats) GetCqPollCount() uint64 {
	if x != nil {
		return x.CqPollCount
	}
	return 0
}

func (x *ClientStats) GetCoreStats() *core.Stats {
	if x != nil {
		return x.CoreStats
	}
	return nil
}

var File_grpc_testing_stats_proto protoreflect.FileDescriptor

const file_grpc_testing_stats_proto_rawDesc = "" +
	"\n" +
	"\x18grpc/testing/stats.proto\x12\fgrpc.testing\x1a\x15grpc/core/stats.proto\"\x8d\x02\n" +
	"\vServerStats\x12!\n" +
	"\ftime_elapsed\x18\x01 \x01(\x01R\vtimeElapsed\x12\x1b\n" +
	"\ttime_user\x18\x02 \x01(\x01R\btimeUser\x12\x1f\n" +
	"\vtime_system\x18\x03 \x01(\x01R\n" +
	"timeSystem\x12$\n" +
	"\x0etotal_cpu_time\x18\x04 \x01(\x04R\ftotalCpuTime\x12\"\n" +
	"\ridle_cpu_time\x18\x05 \x01(\x04R\vidleCpuTime\x12\"\n" +
	"\rcq_poll_count\x18\x06 \x01(\x04R\vcqPollCount\x12/\n" +
	"\n" +
	"core_stats\x18\a \x01(\v2\x10.grpc.core.StatsR\tcoreStats\"T\n" +
	"\x0fHistogramParams\x12\x1e\n" +
	"\n" +
	"resolution\x18\x01 \x01(\x01R\n" +
	"resolution\x12!\n" +
	"\fmax_possible\x18\x02 \x01(\x01R\vmaxPossible\"\xab\x01\n" +
	"\rHistogramData\x12\x16\n" +
	"\x06bucket\x18\x01 \x03(\rR\x06bucket\x12\x19\n" +
	"\bmin_seen\x18\x02 \x01(\x01R\aminSeen\x12\x19\n" +
	"\bmax_seen\x18\x03 \x01(\x01R\amaxSeen\x12\x10\n" +
	"\x03sum\x18\x04 \x01(\x01R\x03sum\x12$\n" +
	"\x0esum_of_squares\x18\x05 \x01(\x01R\fsumOfSquares\x12\x14\n" +
	"\x05count\x18\x06 \x01(\x01R\x05count\"K\n" +
	"\x12RequestResultCount\x12\x1f\n" +
	"\vstatus_code\x18\x01 \x01(\x05R\n" +
	"statusCode\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x03R\x05count\"\xc9\x02\n" +
	"\vClientStats\x129\n" +
	"\tlatencies\x18\x01 \x01(\v2\x1b.grpc.testing.HistogramDataR\tlatencies\x12!\n" +
	"\ftime_elapsed\x18\x02 \x01(\x01R\vtimeElapsed\x12\x1b\n" +
	"\ttime_user\x18\x03 \x01(\x01R\btimeUser\x12\x1f\n" +
	"\vtime_system\x18\x04 \x01(\x01R\n" +
	"timeSystem\x12I\n" +
	"\x0frequest_results\x18\x05 \x03(\v2 .grpc.testing.RequestResultCountR\x0erequestResults\x12\"\n" +
	"\rcq_poll_count\x18\x06 \x01(\x04R\vcqPollCount\x12/\n" +
	"\n" +
	"core_stats\x18\a \x01(\v2\x10.grpc.core.StatsR\tcoreStatsB\x1f\n" +
	"\x0fio.grpc.testingB\n" +
	"StatsProtoP\x01b\x06proto3"

var (
	file_grpc_testing_stats_proto_rawDescOnce sync.Once
	file_grpc_testing_stats_proto_rawDescData []byte
)

func file_grpc_testing_stats_proto_rawDescGZIP() []byte {
	file_grpc_testing_stats_proto_rawDescOnce.Do(func() {
		file_grpc_testing_stats_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_grpc_testing_stats_proto_rawDesc), len(file_grpc_testing_stats_proto_rawDesc)))
	})
	return file_grpc_testing_stats_proto_rawDescData
}

var file_grpc_testing_stats_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_grpc_testing_stats_proto_goTypes = []any{
	(*ServerStats)(nil),        // 0: grpc.testing.ServerStats
	(*HistogramParams)(nil),    // 1: grpc.testing.HistogramParams
	(*HistogramData)(nil),      // 2: grpc.testing.HistogramData
	(*RequestResultCount)(nil), // 3: grpc.testing.RequestResultCount
	(*ClientStats)(nil),        // 4: grpc.testing.ClientStats
	(*core.Stats)(nil),         // 5: grpc.core.Stats
}
var file_grpc_testing_stats_proto_depIdxs = []int32{
	5, // 0: grpc.testing.ServerStats.core_stats:type_name -> grpc.core.Stats
	2, // 1: grpc.testing.ClientStats.latencies:type_name -> grpc.testing.HistogramData
	3, // 2: grpc.testing.ClientStats.request_results:type_name -> grpc.testing.RequestResultCount
	5, // 3: grpc.testing.ClientStats.core_stats:type_name -> grpc.core.Stats
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_grpc_testing_stats_proto_init() }
func file_grpc_testing_stats_proto_init() {
	if File_grpc_testing_stats_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_grpc_testing_stats_proto_rawDesc), len(file_grpc_testing_stats_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_grpc_testing_stats_proto_goTypes,
		DependencyIndexes: file_grpc_testing_stats_proto_depIdxs,
		MessageInfos:      file_grpc_testing_stats_proto_msgTypes,
	}.Build()
	File_grpc_testing_stats_proto = out.File
	file_grpc_testing_stats_proto_goTypes = nil
	file_grpc_testing_stats_proto_depIdxs = nil
}
