// Copyright 2017 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: grpc/core/stats.proto

package core

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bucket struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Start         float64                `protobuf:"fixed64,1,opt,name=start,proto3" json:"start,omitempty"`
	Count         uint64                 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bucket) Reset() {
	*x = Bucket{}
	mi := &file_grpc_core_stats_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bucket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bucket) ProtoMessage() {}

func (x *Bucket) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_core_stats_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bucket.ProtoReflect.Descriptor instead.
func (*Bucket) Descriptor() ([]byte, []int) {
	return file_grpc_core_stats_proto_rawDescGZIP(), []int{0}
}

func (x *Bucket) GetStart() float64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *Bucket) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type Histogram struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Buckets       []*Bucket              `protobuf:"bytes,1,rep,name=buckets,proto3" json:"buckets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Histogram) Reset() {
	*x = Histogram{}
	mi := &file_grpc_core_stats_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Histogram) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Histogram) ProtoMessage() {}

func (x *Histogram) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_core_stats_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Histogram.ProtoReflect.Descriptor instead.
func (*Histogram) Descriptor() ([]byte, []int) {
	return file_grpc_core_stats_proto_rawDescGZIP(), []int{1}
}

func (x *Histogram) GetBuckets() []*Bucket {
	if x != nil {
		return x.Buckets
	}
	return nil
}

type Metric struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Name  string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Types that are valid to be assigned to Value:
	//
	//	*Metric_Count
	//	*Metric_Histogram
	Value         isMetric_Value `protobuf_oneof:"value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Metric) Reset() {
	*x = Metric{}
	mi := &file_grpc_core_stats_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Metric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metric) ProtoMessage() {}

func (x *Metric) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_core_stats_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metric.ProtoReflect.Descriptor instead.
func (*Metric) Descriptor() ([]byte, []int) {
	return file_grpc_core_stats_proto_rawDescGZIP(), []int{2}
}

func (x *Metric) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Metric) GetValue() isMetric_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *Metric) GetCount() uint64 {
	if x != nil {
		if x, ok := x.Value.(*Metric_Count); ok {
			return x.Count
		}
	}
	return 0
}

func (x *Metric) GetHistogram() *Histogram {
	if x != nil {
		if x, ok := x.Value.(*Metric_Histogram); ok {
			return x.Histogram
		}
	}
	return nil
}

type isMetric_Value interface {
	isMetric_Value()
}

type Metric_Count struct {
	Count uint64 `protobuf:"varint,10,opt,name=count,proto3,oneof"`
}

type Metric_Histogram struct {
	Histogram *Histogram `protobuf:"bytes,11,opt,name=histogram,proto3,oneof"`
}

func (*Metric_Count) isMetric_Value() {}

func (*Metric_Histogram) isMetric_Value() {}

type Stats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metrics       []*Metric              `protobuf:"bytes,1,rep,name=metrics,proto3" json:"metrics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Stats) Reset() {
	*x = Stats{}
	mi := &file_grpc_core_stats_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Stats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Stats) ProtoMessage() {}

func (x *Stats) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_core_stats_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Stats.ProtoReflect.Descriptor instead.
func (*Stats) Descriptor() ([]byte, []int) {
	return file_grpc_core_stats_proto_rawDescGZIP(), []int{3}
}

func (x *Stats) GetMetrics() []*Metric {
	if x != nil {
		return x.Metrics
	}
	return nil
}

var File_grpc_core_stats_proto protoreflect.FileDescriptor

const file_grpc_core_stats_proto_rawDesc = "" +
	"\n" +
	"\x15grpc/core/stats.proto\x12\tgrpc.core\"4\n" +
	"\x06Bucket\x12\x14\n" +
	"\x05start\x18\x01 \x01(\x01R\x05start\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x04R\x05count\"8\n" +
	"\tHistogram\x12+\n" +
	"\abuckets\x18\x01 \x03(\v2\x11.grpc.core.BucketR\abuckets\"s\n" +
	"\x06Metric\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x05count\x18\n" +
	" \x01(\x04H\x00R\x05count\x124\n" +
	"\thistogram\x18\v \x01(\v2\x14.grpc.core.HistogramH\x00R\thistogramB\a\n" +
	"\x05value\"4\n" +
	"\x05Stats\x12+\n" +
	"\ametrics\x18\x01 \x03(\v2\x11.grpc.core.MetricR\ametricsb\x06proto3"

var (
	file_grpc_core_stats_proto_rawDescOnce sync.Once
	file_grpc_core_stats_proto_rawDescData []byte
)

func file_grpc_core_stats_proto_rawDescGZIP() []byte {
	file_grpc_core_stats_proto_rawDescOnce.Do(func() {
		file_grpc_core_stats_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_grpc_core_stats_proto_rawDesc), len(file_grpc_core_stats_proto_rawDesc)))
	})
	return file_grpc_core_stats_proto_rawDescData
}

var file_grpc_core_stats_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_grpc_core_stats_proto_goTypes = []any{
	(*Bucket)(nil),    // 0: grpc.core.Bucket
	(*Histogram)(nil), // 1: grpc.core.Histogram
	(*Metric)(nil),    // 2: grpc.core.Metric
	(*Stats)(nil),     // 3: grpc.core.Stats
}
var file_grpc_core_stats_proto_depIdxs = []int32{
	0, // 0: grpc.core.Histogram.buckets:type_name -> grpc.core.Bucket
	1, // 1: grpc.core.Metric.histogram:type_name -> grpc.core.Histogram
	2, // 2: grpc.core.Stats.metrics:type_name -> grpc.core.Metric
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_grpc_core_stats_proto_init() }
func file_grpc_core_stats_proto_init() {
	if File_grpc_core_stats_proto != nil {
		return
	}
	file_grpc_core_stats_proto_msgTypes[2].OneofWrappers = []any{
		(*Metric_Count)(nil),
		(*Metric_Histogram)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_grpc_core_stats_proto_rawDesc), len(file_grpc_core_stats_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_grpc_core_stats_proto_goTypes,
		DependencyIndexes: file_grpc_core_stats_proto_depIdxs,
		MessageInfos:      file_grpc_core_stats_proto_msgTypes,
	}.Build()
	File_grpc_core_stats_proto = out.File
	file_grpc_core_stats_proto_goTypes = nil
	file_grpc_core_stats_proto_depIdxs = nil
}
