// Copyright 2015-2016 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// An integration test service that covers all the method signature permutations
// of unary/streaming requests/responses.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: grpc/testing/test.proto

package grpc_testing

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_grpc_testing_test_proto protoreflect.FileDescriptor

const file_grpc_testing_test_proto_rawDesc = "" +
	"\n" +
	"\x17grpc/testing/test.proto\x12\fgrpc.testing\x1a\x18grpc/testing/empty.proto\x1a\x1bgrpc/testing/messages.proto2\xcb\x05\n" +
	"\vTestService\x125\n" +
	"\tEmptyCall\x12\x13.grpc.testing.Empty\x1a\x13.grpc.testing.Empty\x12F\n" +
	"\tUnaryCall\x12\x1b.grpc.testing.SimpleRequest\x1a\x1c.grpc.testing.SimpleResponse\x12O\n" +
	"\x12CacheableUnaryCall\x12\x1b.grpc.testing.SimpleRequest\x1a\x1c.grpc.testing.SimpleResponse\x12l\n" +
	"\x13StreamingOutputCall\x12(.grpc.testing.StreamingOutputCallRequest\x1a).grpc.testing.StreamingOutputCallResponse0\x01\x12i\n" +
	"\x12StreamingInputCall\x12'.grpc.testing.StreamingInputCallRequest\x1a(.grpc.testing.StreamingInputCallResponse(\x01\x12i\n" +
	"\x0eFullDuplexCall\x12(.grpc.testing.StreamingOutputCallRequest\x1a).grpc.testing.StreamingOutputCallResponse(\x010\x01\x12i\n" +
	"\x0eHalfDuplexCall\x12(.grpc.testing.StreamingOutputCallRequest\x1a).grpc.testing.StreamingOutputCallResponse(\x010\x01\x12=\n" +
	"\x11UnimplementedCall\x12\x13.grpc.testing.Empty\x1a\x13.grpc.testing.Empty2U\n" +
	"\x14UnimplementedService\x12=\n" +
	"\x11UnimplementedCall\x12\x13.grpc.testing.Empty\x1a\x13.grpc.testing.Empty2\x89\x01\n" +
	"\x10ReconnectService\x12;\n" +
	"\x05Start\x12\x1d.grpc.testing.ReconnectParams\x1a\x13.grpc.testing.Empty\x128\n" +
	"\x04Stop\x12\x13.grpc.testing.Empty\x1a\x1b.grpc.testing.ReconnectInfo2\x86\x02\n" +
	"\x18LoadBalancerStatsService\x12c\n" +
	"\x0eGetClientStats\x12&.grpc.testing.LoadBalancerStatsRequest\x1a'.grpc.testing.LoadBalancerStatsResponse\"\x00\x12\x84\x01\n" +
	"\x19GetClientAccumulatedStats\x121.grpc.testing.LoadBalancerAccumulatedStatsRequest\x1a2.grpc.testing.LoadBalancerAccumulatedStatsResponse\"\x002\xcc\x01\n" +
	"\vHookService\x120\n" +
	"\x04Hook\x12\x13.grpc.testing.Empty\x1a\x13.grpc.testing.Empty\x12L\n" +
	"\x0fSetReturnStatus\x12$.grpc.testing.SetReturnStatusRequest\x1a\x13.grpc.testing.Empty\x12=\n" +
	"\x11ClearReturnStatus\x12\x13.grpc.testing.Empty\x1a\x13.grpc.testing.Empty2\xd5\x01\n" +
	"\x16XdsUpdateHealthService\x126\n" +
	"\n" +
	"SetServing\x12\x13.grpc.testing.Empty\x1a\x13.grpc.testing.Empty\x129\n" +
	"\rSetNotServing\x12\x13.grpc.testing.Empty\x1a\x13.grpc.testing.Empty\x12H\n" +
	"\x0fSendHookRequest\x12\x19.grpc.testing.HookRequest\x1a\x1a.grpc.testing.HookResponse2{\n" +
	"\x1fXdsUpdateClientConfigureService\x12X\n" +
	"\tConfigure\x12$.grpc.testing.ClientConfigureRequest\x1a%.grpc.testing.ClientConfigureResponseB\x1d\n" +
	"\x1bio.grpc.testing.integrationb\x06proto3"

var file_grpc_testing_test_proto_goTypes = []any{
	(*Empty)(nil),                                // 0: grpc.testing.Empty
	(*SimpleRequest)(nil),                        // 1: grpc.testing.SimpleRequest
	(*StreamingOutputCallRequest)(nil),           // 2: grpc.testing.StreamingOutputCallRequest
	(*StreamingInputCallRequest)(nil),            // 3: grpc.testing.StreamingInputCallRequest
	(*ReconnectParams)(nil),                      // 4: grpc.testing.ReconnectParams
	(*LoadBalancerStatsRequest)(nil),             // 5: grpc.testing.LoadBalancerStatsRequest
	(*LoadBalancerAccumulatedStatsRequest)(nil),  // 6: grpc.testing.LoadBalancerAccumulatedStatsRequest
	(*SetReturnStatusRequest)(nil),               // 7: grpc.testing.SetReturnStatusRequest
	(*HookRequest)(nil),                          // 8: grpc.testing.HookRequest
	(*ClientConfigureRequest)(nil),               // 9: grpc.testing.ClientConfigureRequest
	(*SimpleResponse)(nil),                       // 10: grpc.testing.SimpleResponse
	(*StreamingOutputCallResponse)(nil),          // 11: grpc.testing.StreamingOutputCallResponse
	(*StreamingInputCallResponse)(nil),           // 12: grpc.testing.StreamingInputCallResponse
	(*ReconnectInfo)(nil),                        // 13: grpc.testing.ReconnectInfo
	(*LoadBalancerStatsResponse)(nil),            // 14: grpc.testing.LoadBalancerStatsResponse
	(*LoadBalancerAccumulatedStatsResponse)(nil), // 15: grpc.testing.LoadBalancerAccumulatedStatsResponse
	(*HookResponse)(nil),                         // 16: grpc.testing.HookResponse
	(*ClientConfigureResponse)(nil),              // 17: grpc.testing.ClientConfigureResponse
}
var file_grpc_testing_test_proto_depIdxs = []int32{
	0,  // 0: grpc.testing.TestService.EmptyCall:input_type -> grpc.testing.Empty
	1,  // 1: grpc.testing.TestService.UnaryCall:input_type -> grpc.testing.SimpleRequest
	1,  // 2: grpc.testing.TestService.CacheableUnaryCall:input_type -> grpc.testing.SimpleRequest
	2,  // 3: grpc.testing.TestService.StreamingOutputCall:input_type -> grpc.testing.StreamingOutputCallRequest
	3,  // 4: grpc.testing.TestService.StreamingInputCall:input_type -> grpc.testing.StreamingInputCallRequest
	2,  // 5: grpc.testing.TestService.FullDuplexCall:input_type -> grpc.testing.StreamingOutputCallRequest
	2,  // 6: grpc.testing.TestService.HalfDuplexCall:input_type -> grpc.testing.StreamingOutputCallRequest
	0,  // 7: grpc.testing.TestService.UnimplementedCall:input_type -> grpc.testing.Empty
	0,  // 8: grpc.testing.UnimplementedService.UnimplementedCall:input_type -> grpc.testing.Empty
	4,  // 9: grpc.testing.ReconnectService.Start:input_type -> grpc.testing.ReconnectParams
	0,  // 10: grpc.testing.ReconnectService.Stop:input_type -> grpc.testing.Empty
	5,  // 11: grpc.testing.LoadBalancerStatsService.GetClientStats:input_type -> grpc.testing.LoadBalancerStatsRequest
	6,  // 12: grpc.testing.LoadBalancerStatsService.GetClientAccumulatedStats:input_type -> grpc.testing.LoadBalancerAccumulatedStatsRequest
	0,  // 13: grpc.testing.HookService.Hook:input_type -> grpc.testing.Empty
	7,  // 14: grpc.testing.HookService.SetReturnStatus:input_type -> grpc.testing.SetReturnStatusRequest
	0,  // 15: grpc.testing.HookService.ClearReturnStatus:input_type -> grpc.testing.Empty
	0,  // 16: grpc.testing.XdsUpdateHealthService.SetServing:input_type -> grpc.testing.Empty
	0,  // 17: grpc.testing.XdsUpdateHealthService.SetNotServing:input_type -> grpc.testing.Empty
	8,  // 18: grpc.testing.XdsUpdateHealthService.SendHookRequest:input_type -> grpc.testing.HookRequest
	9,  // 19: grpc.testing.XdsUpdateClientConfigureService.Configure:input_type -> grpc.testing.ClientConfigureRequest
	0,  // 20: grpc.testing.TestService.EmptyCall:output_type -> grpc.testing.Empty
	10, // 21: grpc.testing.TestService.UnaryCall:output_type -> grpc.testing.SimpleResponse
	10, // 22: grpc.testing.TestService.CacheableUnaryCall:output_type -> grpc.testing.SimpleResponse
	11, // 23: grpc.testing.TestService.StreamingOutputCall:output_type -> grpc.testing.StreamingOutputCallResponse
	12, // 24: grpc.testing.TestService.StreamingInputCall:output_type -> grpc.testing.StreamingInputCallResponse
	11, // 25: grpc.testing.TestService.FullDuplexCall:output_type -> grpc.testing.StreamingOutputCallResponse
	11, // 26: grpc.testing.TestService.HalfDuplexCall:output_type -> grpc.testing.StreamingOutputCallResponse
	0,  // 27: grpc.testing.TestService.UnimplementedCall:output_type -> grpc.testing.Empty
	0,  // 28: grpc.testing.UnimplementedService.UnimplementedCall:output_type -> grpc.testing.Empty
	0,  // 29: grpc.testing.ReconnectService.Start:output_type -> grpc.testing.Empty
	13, // 30: grpc.testing.ReconnectService.Stop:output_type -> grpc.testing.ReconnectInfo
	14, // 31: grpc.testing.LoadBalancerStatsService.GetClientStats:output_type -> grpc.testing.LoadBalancerStatsResponse
	15, // 32: grpc.testing.LoadBalancerStatsService.GetClientAccumulatedStats:output_type -> grpc.testing.LoadBalancerAccumulatedStatsResponse
	0,  // 33: grpc.testing.HookService.Hook:output_type -> grpc.testing.Empty
	0,  // 34: grpc.testing.HookService.SetReturnStatus:output_type -> grpc.testing.Empty
	0,  // 35: grpc.testing.HookService.ClearReturnStatus:output_type -> grpc.testing.Empty
	0,  // 36: grpc.testing.XdsUpdateHealthService.SetServing:output_type -> grpc.testing.Empty
	0,  // 37: grpc.testing.XdsUpdateHealthService.SetNotServing:output_type -> grpc.testing.Empty
	16, // 38: grpc.testing.XdsUpdateHealthService.SendHookRequest:output_type -> grpc.testing.HookResponse
	17, // 39: grpc.testing.XdsUpdateClientConfigureService.Configure:output_type -> grpc.testing.ClientConfigureResponse
	20, // [20:40] is the sub-list for method output_type
	0,  // [0:20] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_grpc_testing_test_proto_init() }
func file_grpc_testing_test_proto_init() {
	if File_grpc_testing_test_proto != nil {
		return
	}
	file_grpc_testing_empty_proto_init()
	file_grpc_testing_messages_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_grpc_testing_test_proto_rawDesc), len(file_grpc_testing_test_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   7,
		},
		GoTypes:           file_grpc_testing_test_proto_goTypes,
		DependencyIndexes: file_grpc_testing_test_proto_depIdxs,
	}.Build()
	File_grpc_testing_test_proto = out.File
	file_grpc_testing_test_proto_goTypes = nil
	file_grpc_testing_test_proto_depIdxs = nil
}
