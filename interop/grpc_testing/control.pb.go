// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: grpc/testing/control.proto

package grpc_testing

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ClientType int32

const (
	// Many languages support a basic distinction between using
	// sync or async client, and this allows the specification
	ClientType_SYNC_CLIENT     ClientType = 0
	ClientType_ASYNC_CLIENT    ClientType = 1
	ClientType_OTHER_CLIENT    ClientType = 2 // used for some language-specific variants
	ClientType_CALLBACK_CLIENT ClientType = 3
)

// Enum value maps for ClientType.
var (
	ClientType_name = map[int32]string{
		0: "SYNC_CLIENT",
		1: "ASYNC_CLIENT",
		2: "OTHER_CLIENT",
		3: "CALLBACK_CLIENT",
	}
	ClientType_value = map[string]int32{
		"SYNC_CLIENT":     0,
		"ASYNC_CLIENT":    1,
		"OTHER_CLIENT":    2,
		"CALLBACK_CLIENT": 3,
	}
)

func (x ClientType) Enum() *ClientType {
	p := new(ClientType)
	*p = x
	return p
}

func (x ClientType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientType) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_testing_control_proto_enumTypes[0].Descriptor()
}

func (ClientType) Type() protoreflect.EnumType {
	return &file_grpc_testing_control_proto_enumTypes[0]
}

func (x ClientType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClientType.Descriptor instead.
func (ClientType) EnumDescriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{0}
}

type ServerType int32

const (
	ServerType_SYNC_SERVER          ServerType = 0
	ServerType_ASYNC_SERVER         ServerType = 1
	ServerType_ASYNC_GENERIC_SERVER ServerType = 2
	ServerType_OTHER_SERVER         ServerType = 3 // used for some language-specific variants
	ServerType_CALLBACK_SERVER      ServerType = 4
)

// Enum value maps for ServerType.
var (
	ServerType_name = map[int32]string{
		0: "SYNC_SERVER",
		1: "ASYNC_SERVER",
		2: "ASYNC_GENERIC_SERVER",
		3: "OTHER_SERVER",
		4: "CALLBACK_SERVER",
	}
	ServerType_value = map[string]int32{
		"SYNC_SERVER":          0,
		"ASYNC_SERVER":         1,
		"ASYNC_GENERIC_SERVER": 2,
		"OTHER_SERVER":         3,
		"CALLBACK_SERVER":      4,
	}
)

func (x ServerType) Enum() *ServerType {
	p := new(ServerType)
	*p = x
	return p
}

func (x ServerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServerType) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_testing_control_proto_enumTypes[1].Descriptor()
}

func (ServerType) Type() protoreflect.EnumType {
	return &file_grpc_testing_control_proto_enumTypes[1]
}

func (x ServerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServerType.Descriptor instead.
func (ServerType) EnumDescriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{1}
}

type RpcType int32

const (
	RpcType_UNARY                 RpcType = 0
	RpcType_STREAMING             RpcType = 1
	RpcType_STREAMING_FROM_CLIENT RpcType = 2
	RpcType_STREAMING_FROM_SERVER RpcType = 3
	RpcType_STREAMING_BOTH_WAYS   RpcType = 4
)

// Enum value maps for RpcType.
var (
	RpcType_name = map[int32]string{
		0: "UNARY",
		1: "STREAMING",
		2: "STREAMING_FROM_CLIENT",
		3: "STREAMING_FROM_SERVER",
		4: "STREAMING_BOTH_WAYS",
	}
	RpcType_value = map[string]int32{
		"UNARY":                 0,
		"STREAMING":             1,
		"STREAMING_FROM_CLIENT": 2,
		"STREAMING_FROM_SERVER": 3,
		"STREAMING_BOTH_WAYS":   4,
	}
)

func (x RpcType) Enum() *RpcType {
	p := new(RpcType)
	*p = x
	return p
}

func (x RpcType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RpcType) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_testing_control_proto_enumTypes[2].Descriptor()
}

func (RpcType) Type() protoreflect.EnumType {
	return &file_grpc_testing_control_proto_enumTypes[2]
}

func (x RpcType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RpcType.Descriptor instead.
func (RpcType) EnumDescriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{2}
}

// Parameters of poisson process distribution, which is a good representation
// of activity coming in from independent identical stationary sources.
type PoissonParams struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The rate of arrivals (a.k.a. lambda parameter of the exp distribution).
	OfferedLoad   float64 `protobuf:"fixed64,1,opt,name=offered_load,json=offeredLoad,proto3" json:"offered_load,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PoissonParams) Reset() {
	*x = PoissonParams{}
	mi := &file_grpc_testing_control_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PoissonParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PoissonParams) ProtoMessage() {}

func (x *PoissonParams) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PoissonParams.ProtoReflect.Descriptor instead.
func (*PoissonParams) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{0}
}

func (x *PoissonParams) GetOfferedLoad() float64 {
	if x != nil {
		return x.OfferedLoad
	}
	return 0
}

// Once an RPC finishes, immediately start a new one.
// No configuration parameters needed.
type ClosedLoopParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClosedLoopParams) Reset() {
	*x = ClosedLoopParams{}
	mi := &file_grpc_testing_control_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClosedLoopParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClosedLoopParams) ProtoMessage() {}

func (x *ClosedLoopParams) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClosedLoopParams.ProtoReflect.Descriptor instead.
func (*ClosedLoopParams) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{1}
}

type LoadParams struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Load:
	//
	//	*LoadParams_ClosedLoop
	//	*LoadParams_Poisson
	Load          isLoadParams_Load `protobuf_oneof:"load"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadParams) Reset() {
	*x = LoadParams{}
	mi := &file_grpc_testing_control_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadParams) ProtoMessage() {}

func (x *LoadParams) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadParams.ProtoReflect.Descriptor instead.
func (*LoadParams) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{2}
}

func (x *LoadParams) GetLoad() isLoadParams_Load {
	if x != nil {
		return x.Load
	}
	return nil
}

func (x *LoadParams) GetClosedLoop() *ClosedLoopParams {
	if x != nil {
		if x, ok := x.Load.(*LoadParams_ClosedLoop); ok {
			return x.ClosedLoop
		}
	}
	return nil
}

func (x *LoadParams) GetPoisson() *PoissonParams {
	if x != nil {
		if x, ok := x.Load.(*LoadParams_Poisson); ok {
			return x.Poisson
		}
	}
	return nil
}

type isLoadParams_Load interface {
	isLoadParams_Load()
}

type LoadParams_ClosedLoop struct {
	ClosedLoop *ClosedLoopParams `protobuf:"bytes,1,opt,name=closed_loop,json=closedLoop,proto3,oneof"`
}

type LoadParams_Poisson struct {
	Poisson *PoissonParams `protobuf:"bytes,2,opt,name=poisson,proto3,oneof"`
}

func (*LoadParams_ClosedLoop) isLoadParams_Load() {}

func (*LoadParams_Poisson) isLoadParams_Load() {}

// presence of SecurityParams implies use of TLS
type SecurityParams struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	UseTestCa          bool                   `protobuf:"varint,1,opt,name=use_test_ca,json=useTestCa,proto3" json:"use_test_ca,omitempty"`
	ServerHostOverride string                 `protobuf:"bytes,2,opt,name=server_host_override,json=serverHostOverride,proto3" json:"server_host_override,omitempty"`
	CredType           string                 `protobuf:"bytes,3,opt,name=cred_type,json=credType,proto3" json:"cred_type,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *SecurityParams) Reset() {
	*x = SecurityParams{}
	mi := &file_grpc_testing_control_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecurityParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityParams) ProtoMessage() {}

func (x *SecurityParams) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityParams.ProtoReflect.Descriptor instead.
func (*SecurityParams) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{3}
}

func (x *SecurityParams) GetUseTestCa() bool {
	if x != nil {
		return x.UseTestCa
	}
	return false
}

func (x *SecurityParams) GetServerHostOverride() string {
	if x != nil {
		return x.ServerHostOverride
	}
	return ""
}

func (x *SecurityParams) GetCredType() string {
	if x != nil {
		return x.CredType
	}
	return ""
}

type ChannelArg struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Name  string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Types that are valid to be assigned to Value:
	//
	//	*ChannelArg_StrValue
	//	*ChannelArg_IntValue
	Value         isChannelArg_Value `protobuf_oneof:"value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChannelArg) Reset() {
	*x = ChannelArg{}
	mi := &file_grpc_testing_control_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChannelArg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelArg) ProtoMessage() {}

func (x *ChannelArg) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelArg.ProtoReflect.Descriptor instead.
func (*ChannelArg) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{4}
}

func (x *ChannelArg) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChannelArg) GetValue() isChannelArg_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *ChannelArg) GetStrValue() string {
	if x != nil {
		if x, ok := x.Value.(*ChannelArg_StrValue); ok {
			return x.StrValue
		}
	}
	return ""
}

func (x *ChannelArg) GetIntValue() int32 {
	if x != nil {
		if x, ok := x.Value.(*ChannelArg_IntValue); ok {
			return x.IntValue
		}
	}
	return 0
}

type isChannelArg_Value interface {
	isChannelArg_Value()
}

type ChannelArg_StrValue struct {
	StrValue string `protobuf:"bytes,2,opt,name=str_value,json=strValue,proto3,oneof"`
}

type ChannelArg_IntValue struct {
	IntValue int32 `protobuf:"varint,3,opt,name=int_value,json=intValue,proto3,oneof"`
}

func (*ChannelArg_StrValue) isChannelArg_Value() {}

func (*ChannelArg_IntValue) isChannelArg_Value() {}

type ClientConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// List of targets to connect to. At least one target needs to be specified.
	ServerTargets  []string        `protobuf:"bytes,1,rep,name=server_targets,json=serverTargets,proto3" json:"server_targets,omitempty"`
	ClientType     ClientType      `protobuf:"varint,2,opt,name=client_type,json=clientType,proto3,enum=grpc.testing.ClientType" json:"client_type,omitempty"`
	SecurityParams *SecurityParams `protobuf:"bytes,3,opt,name=security_params,json=securityParams,proto3" json:"security_params,omitempty"`
	// How many concurrent RPCs to start for each channel.
	// For synchronous client, use a separate thread for each outstanding RPC.
	OutstandingRpcsPerChannel int32 `protobuf:"varint,4,opt,name=outstanding_rpcs_per_channel,json=outstandingRpcsPerChannel,proto3" json:"outstanding_rpcs_per_channel,omitempty"`
	// Number of independent client channels to create.
	// i-th channel will connect to server_target[i % server_targets.size()]
	ClientChannels int32 `protobuf:"varint,5,opt,name=client_channels,json=clientChannels,proto3" json:"client_channels,omitempty"`
	// Only for async client. Number of threads to use to start/manage RPCs.
	AsyncClientThreads int32   `protobuf:"varint,7,opt,name=async_client_threads,json=asyncClientThreads,proto3" json:"async_client_threads,omitempty"`
	RpcType            RpcType `protobuf:"varint,8,opt,name=rpc_type,json=rpcType,proto3,enum=grpc.testing.RpcType" json:"rpc_type,omitempty"`
	// The requested load for the entire client (aggregated over all the threads).
	LoadParams      *LoadParams      `protobuf:"bytes,10,opt,name=load_params,json=loadParams,proto3" json:"load_params,omitempty"`
	PayloadConfig   *PayloadConfig   `protobuf:"bytes,11,opt,name=payload_config,json=payloadConfig,proto3" json:"payload_config,omitempty"`
	HistogramParams *HistogramParams `protobuf:"bytes,12,opt,name=histogram_params,json=histogramParams,proto3" json:"histogram_params,omitempty"`
	// Specify the cores we should run the client on, if desired
	CoreList  []int32 `protobuf:"varint,13,rep,packed,name=core_list,json=coreList,proto3" json:"core_list,omitempty"`
	CoreLimit int32   `protobuf:"varint,14,opt,name=core_limit,json=coreLimit,proto3" json:"core_limit,omitempty"`
	// If we use an OTHER_CLIENT client_type, this string gives more detail
	OtherClientApi string        `protobuf:"bytes,15,opt,name=other_client_api,json=otherClientApi,proto3" json:"other_client_api,omitempty"`
	ChannelArgs    []*ChannelArg `protobuf:"bytes,16,rep,name=channel_args,json=channelArgs,proto3" json:"channel_args,omitempty"`
	// Number of threads that share each completion queue
	ThreadsPerCq int32 `protobuf:"varint,17,opt,name=threads_per_cq,json=threadsPerCq,proto3" json:"threads_per_cq,omitempty"`
	// Number of messages on a stream before it gets finished/restarted
	MessagesPerStream int32 `protobuf:"varint,18,opt,name=messages_per_stream,json=messagesPerStream,proto3" json:"messages_per_stream,omitempty"`
	// Use coalescing API when possible.
	UseCoalesceApi bool `protobuf:"varint,19,opt,name=use_coalesce_api,json=useCoalesceApi,proto3" json:"use_coalesce_api,omitempty"`
	// If 0, disabled. Else, specifies the period between gathering latency
	// medians in milliseconds.
	MedianLatencyCollectionIntervalMillis int32 `protobuf:"varint,20,opt,name=median_latency_collection_interval_millis,json=medianLatencyCollectionIntervalMillis,proto3" json:"median_latency_collection_interval_millis,omitempty"`
	// Number of client processes. 0 indicates no restriction.
	ClientProcesses int32 `protobuf:"varint,21,opt,name=client_processes,json=clientProcesses,proto3" json:"client_processes,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ClientConfig) Reset() {
	*x = ClientConfig{}
	mi := &file_grpc_testing_control_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientConfig) ProtoMessage() {}

func (x *ClientConfig) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientConfig.ProtoReflect.Descriptor instead.
func (*ClientConfig) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{5}
}

func (x *ClientConfig) GetServerTargets() []string {
	if x != nil {
		return x.ServerTargets
	}
	return nil
}

func (x *ClientConfig) GetClientType() ClientType {
	if x != nil {
		return x.ClientType
	}
	return ClientType_SYNC_CLIENT
}

func (x *ClientConfig) GetSecurityParams() *SecurityParams {
	if x != nil {
		return x.SecurityParams
	}
	return nil
}

func (x *ClientConfig) GetOutstandingRpcsPerChannel() int32 {
	if x != nil {
		return x.OutstandingRpcsPerChannel
	}
	return 0
}

func (x *ClientConfig) GetClientChannels() int32 {
	if x != nil {
		return x.ClientChannels
	}
	return 0
}

func (x *ClientConfig) GetAsyncClientThreads() int32 {
	if x != nil {
		return x.AsyncClientThreads
	}
	return 0
}

func (x *ClientConfig) GetRpcType() RpcType {
	if x != nil {
		return x.RpcType
	}
	return RpcType_UNARY
}

func (x *ClientConfig) GetLoadParams() *LoadParams {
	if x != nil {
		return x.LoadParams
	}
	return nil
}

func (x *ClientConfig) GetPayloadConfig() *PayloadConfig {
	if x != nil {
		return x.PayloadConfig
	}
	return nil
}

func (x *ClientConfig) GetHistogramParams() *HistogramParams {
	if x != nil {
		return x.HistogramParams
	}
	return nil
}

func (x *ClientConfig) GetCoreList() []int32 {
	if x != nil {
		return x.CoreList
	}
	return nil
}

func (x *ClientConfig) GetCoreLimit() int32 {
	if x != nil {
		return x.CoreLimit
	}
	return 0
}

func (x *ClientConfig) GetOtherClientApi() string {
	if x != nil {
		return x.OtherClientApi
	}
	return ""
}

func (x *ClientConfig) GetChannelArgs() []*ChannelArg {
	if x != nil {
		return x.ChannelArgs
	}
	return nil
}

func (x *ClientConfig) GetThreadsPerCq() int32 {
	if x != nil {
		return x.ThreadsPerCq
	}
	return 0
}

func (x *ClientConfig) GetMessagesPerStream() int32 {
	if x != nil {
		return x.MessagesPerStream
	}
	return 0
}

func (x *ClientConfig) GetUseCoalesceApi() bool {
	if x != nil {
		return x.UseCoalesceApi
	}
	return false
}

func (x *ClientConfig) GetMedianLatencyCollectionIntervalMillis() int32 {
	if x != nil {
		return x.MedianLatencyCollectionIntervalMillis
	}
	return 0
}

func (x *ClientConfig) GetClientProcesses() int32 {
	if x != nil {
		return x.ClientProcesses
	}
	return 0
}

type ClientStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stats         *ClientStats           `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientStatus) Reset() {
	*x = ClientStatus{}
	mi := &file_grpc_testing_control_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientStatus) ProtoMessage() {}

func (x *ClientStatus) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientStatus.ProtoReflect.Descriptor instead.
func (*ClientStatus) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{6}
}

func (x *ClientStatus) GetStats() *ClientStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

// Request current stats
type Mark struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// if true, the stats will be reset after taking their snapshot.
	Reset_        bool `protobuf:"varint,1,opt,name=reset,proto3" json:"reset,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Mark) Reset() {
	*x = Mark{}
	mi := &file_grpc_testing_control_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Mark) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mark) ProtoMessage() {}

func (x *Mark) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mark.ProtoReflect.Descriptor instead.
func (*Mark) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{7}
}

func (x *Mark) GetReset_() bool {
	if x != nil {
		return x.Reset_
	}
	return false
}

type ClientArgs struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Argtype:
	//
	//	*ClientArgs_Setup
	//	*ClientArgs_Mark
	Argtype       isClientArgs_Argtype `protobuf_oneof:"argtype"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientArgs) Reset() {
	*x = ClientArgs{}
	mi := &file_grpc_testing_control_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientArgs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientArgs) ProtoMessage() {}

func (x *ClientArgs) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientArgs.ProtoReflect.Descriptor instead.
func (*ClientArgs) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{8}
}

func (x *ClientArgs) GetArgtype() isClientArgs_Argtype {
	if x != nil {
		return x.Argtype
	}
	return nil
}

func (x *ClientArgs) GetSetup() *ClientConfig {
	if x != nil {
		if x, ok := x.Argtype.(*ClientArgs_Setup); ok {
			return x.Setup
		}
	}
	return nil
}

func (x *ClientArgs) GetMark() *Mark {
	if x != nil {
		if x, ok := x.Argtype.(*ClientArgs_Mark); ok {
			return x.Mark
		}
	}
	return nil
}

type isClientArgs_Argtype interface {
	isClientArgs_Argtype()
}

type ClientArgs_Setup struct {
	Setup *ClientConfig `protobuf:"bytes,1,opt,name=setup,proto3,oneof"`
}

type ClientArgs_Mark struct {
	Mark *Mark `protobuf:"bytes,2,opt,name=mark,proto3,oneof"`
}

func (*ClientArgs_Setup) isClientArgs_Argtype() {}

func (*ClientArgs_Mark) isClientArgs_Argtype() {}

type ServerConfig struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ServerType     ServerType             `protobuf:"varint,1,opt,name=server_type,json=serverType,proto3,enum=grpc.testing.ServerType" json:"server_type,omitempty"`
	SecurityParams *SecurityParams        `protobuf:"bytes,2,opt,name=security_params,json=securityParams,proto3" json:"security_params,omitempty"`
	// Port on which to listen. Zero means pick unused port.
	Port int32 `protobuf:"varint,4,opt,name=port,proto3" json:"port,omitempty"`
	// Only for async server. Number of threads used to serve the requests.
	AsyncServerThreads int32 `protobuf:"varint,7,opt,name=async_server_threads,json=asyncServerThreads,proto3" json:"async_server_threads,omitempty"`
	// Specify the number of cores to limit server to, if desired
	CoreLimit int32 `protobuf:"varint,8,opt,name=core_limit,json=coreLimit,proto3" json:"core_limit,omitempty"`
	// payload config, used in generic server.
	// Note this must NOT be used in proto (non-generic) servers. For proto servers,
	// 'response sizes' must be configured from the 'response_size' field of the
	// 'SimpleRequest' objects in RPC requests.
	PayloadConfig *PayloadConfig `protobuf:"bytes,9,opt,name=payload_config,json=payloadConfig,proto3" json:"payload_config,omitempty"`
	// Specify the cores we should run the server on, if desired
	CoreList []int32 `protobuf:"varint,10,rep,packed,name=core_list,json=coreList,proto3" json:"core_list,omitempty"`
	// If we use an OTHER_SERVER client_type, this string gives more detail
	OtherServerApi string `protobuf:"bytes,11,opt,name=other_server_api,json=otherServerApi,proto3" json:"other_server_api,omitempty"`
	// Number of threads that share each completion queue
	ThreadsPerCq int32 `protobuf:"varint,12,opt,name=threads_per_cq,json=threadsPerCq,proto3" json:"threads_per_cq,omitempty"`
	// Buffer pool size (no buffer pool specified if unset)
	ResourceQuotaSize int32         `protobuf:"varint,1001,opt,name=resource_quota_size,json=resourceQuotaSize,proto3" json:"resource_quota_size,omitempty"`
	ChannelArgs       []*ChannelArg `protobuf:"bytes,1002,rep,name=channel_args,json=channelArgs,proto3" json:"channel_args,omitempty"`
	// Number of server processes. 0 indicates no restriction.
	ServerProcesses int32 `protobuf:"varint,21,opt,name=server_processes,json=serverProcesses,proto3" json:"server_processes,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ServerConfig) Reset() {
	*x = ServerConfig{}
	mi := &file_grpc_testing_control_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerConfig) ProtoMessage() {}

func (x *ServerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerConfig.ProtoReflect.Descriptor instead.
func (*ServerConfig) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{9}
}

func (x *ServerConfig) GetServerType() ServerType {
	if x != nil {
		return x.ServerType
	}
	return ServerType_SYNC_SERVER
}

func (x *ServerConfig) GetSecurityParams() *SecurityParams {
	if x != nil {
		return x.SecurityParams
	}
	return nil
}

func (x *ServerConfig) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ServerConfig) GetAsyncServerThreads() int32 {
	if x != nil {
		return x.AsyncServerThreads
	}
	return 0
}

func (x *ServerConfig) GetCoreLimit() int32 {
	if x != nil {
		return x.CoreLimit
	}
	return 0
}

func (x *ServerConfig) GetPayloadConfig() *PayloadConfig {
	if x != nil {
		return x.PayloadConfig
	}
	return nil
}

func (x *ServerConfig) GetCoreList() []int32 {
	if x != nil {
		return x.CoreList
	}
	return nil
}

func (x *ServerConfig) GetOtherServerApi() string {
	if x != nil {
		return x.OtherServerApi
	}
	return ""
}

func (x *ServerConfig) GetThreadsPerCq() int32 {
	if x != nil {
		return x.ThreadsPerCq
	}
	return 0
}

func (x *ServerConfig) GetResourceQuotaSize() int32 {
	if x != nil {
		return x.ResourceQuotaSize
	}
	return 0
}

func (x *ServerConfig) GetChannelArgs() []*ChannelArg {
	if x != nil {
		return x.ChannelArgs
	}
	return nil
}

func (x *ServerConfig) GetServerProcesses() int32 {
	if x != nil {
		return x.ServerProcesses
	}
	return 0
}

type ServerArgs struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Argtype:
	//
	//	*ServerArgs_Setup
	//	*ServerArgs_Mark
	Argtype       isServerArgs_Argtype `protobuf_oneof:"argtype"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerArgs) Reset() {
	*x = ServerArgs{}
	mi := &file_grpc_testing_control_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerArgs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerArgs) ProtoMessage() {}

func (x *ServerArgs) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerArgs.ProtoReflect.Descriptor instead.
func (*ServerArgs) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{10}
}

func (x *ServerArgs) GetArgtype() isServerArgs_Argtype {
	if x != nil {
		return x.Argtype
	}
	return nil
}

func (x *ServerArgs) GetSetup() *ServerConfig {
	if x != nil {
		if x, ok := x.Argtype.(*ServerArgs_Setup); ok {
			return x.Setup
		}
	}
	return nil
}

func (x *ServerArgs) GetMark() *Mark {
	if x != nil {
		if x, ok := x.Argtype.(*ServerArgs_Mark); ok {
			return x.Mark
		}
	}
	return nil
}

type isServerArgs_Argtype interface {
	isServerArgs_Argtype()
}

type ServerArgs_Setup struct {
	Setup *ServerConfig `protobuf:"bytes,1,opt,name=setup,proto3,oneof"`
}

type ServerArgs_Mark struct {
	Mark *Mark `protobuf:"bytes,2,opt,name=mark,proto3,oneof"`
}

func (*ServerArgs_Setup) isServerArgs_Argtype() {}

func (*ServerArgs_Mark) isServerArgs_Argtype() {}

type ServerStatus struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Stats *ServerStats           `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	// the port bound by the server
	Port int32 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	// Number of cores available to the server
	Cores         int32 `protobuf:"varint,3,opt,name=cores,proto3" json:"cores,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerStatus) Reset() {
	*x = ServerStatus{}
	mi := &file_grpc_testing_control_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerStatus) ProtoMessage() {}

func (x *ServerStatus) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerStatus.ProtoReflect.Descriptor instead.
func (*ServerStatus) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{11}
}

func (x *ServerStatus) GetStats() *ServerStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *ServerStatus) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ServerStatus) GetCores() int32 {
	if x != nil {
		return x.Cores
	}
	return 0
}

type CoreRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CoreRequest) Reset() {
	*x = CoreRequest{}
	mi := &file_grpc_testing_control_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoreRequest) ProtoMessage() {}

func (x *CoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoreRequest.ProtoReflect.Descriptor instead.
func (*CoreRequest) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{12}
}

type CoreResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Number of cores available on the server
	Cores         int32 `protobuf:"varint,1,opt,name=cores,proto3" json:"cores,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CoreResponse) Reset() {
	*x = CoreResponse{}
	mi := &file_grpc_testing_control_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CoreResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoreResponse) ProtoMessage() {}

func (x *CoreResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoreResponse.ProtoReflect.Descriptor instead.
func (*CoreResponse) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{13}
}

func (x *CoreResponse) GetCores() int32 {
	if x != nil {
		return x.Cores
	}
	return 0
}

type Void struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Void) Reset() {
	*x = Void{}
	mi := &file_grpc_testing_control_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Void) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Void) ProtoMessage() {}

func (x *Void) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Void.ProtoReflect.Descriptor instead.
func (*Void) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{14}
}

// A single performance scenario: input to qps_json_driver
type Scenario struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Human readable name for this scenario
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Client configuration
	ClientConfig *ClientConfig `protobuf:"bytes,2,opt,name=client_config,json=clientConfig,proto3" json:"client_config,omitempty"`
	// Number of clients to start for the test
	NumClients int32 `protobuf:"varint,3,opt,name=num_clients,json=numClients,proto3" json:"num_clients,omitempty"`
	// Server configuration
	ServerConfig *ServerConfig `protobuf:"bytes,4,opt,name=server_config,json=serverConfig,proto3" json:"server_config,omitempty"`
	// Number of servers to start for the test
	NumServers int32 `protobuf:"varint,5,opt,name=num_servers,json=numServers,proto3" json:"num_servers,omitempty"`
	// Warmup period, in seconds
	WarmupSeconds int32 `protobuf:"varint,6,opt,name=warmup_seconds,json=warmupSeconds,proto3" json:"warmup_seconds,omitempty"`
	// Benchmark time, in seconds
	BenchmarkSeconds int32 `protobuf:"varint,7,opt,name=benchmark_seconds,json=benchmarkSeconds,proto3" json:"benchmark_seconds,omitempty"`
	// Number of workers to spawn locally (usually zero)
	SpawnLocalWorkerCount int32 `protobuf:"varint,8,opt,name=spawn_local_worker_count,json=spawnLocalWorkerCount,proto3" json:"spawn_local_worker_count,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *Scenario) Reset() {
	*x = Scenario{}
	mi := &file_grpc_testing_control_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Scenario) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scenario) ProtoMessage() {}

func (x *Scenario) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scenario.ProtoReflect.Descriptor instead.
func (*Scenario) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{15}
}

func (x *Scenario) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Scenario) GetClientConfig() *ClientConfig {
	if x != nil {
		return x.ClientConfig
	}
	return nil
}

func (x *Scenario) GetNumClients() int32 {
	if x != nil {
		return x.NumClients
	}
	return 0
}

func (x *Scenario) GetServerConfig() *ServerConfig {
	if x != nil {
		return x.ServerConfig
	}
	return nil
}

func (x *Scenario) GetNumServers() int32 {
	if x != nil {
		return x.NumServers
	}
	return 0
}

func (x *Scenario) GetWarmupSeconds() int32 {
	if x != nil {
		return x.WarmupSeconds
	}
	return 0
}

func (x *Scenario) GetBenchmarkSeconds() int32 {
	if x != nil {
		return x.BenchmarkSeconds
	}
	return 0
}

func (x *Scenario) GetSpawnLocalWorkerCount() int32 {
	if x != nil {
		return x.SpawnLocalWorkerCount
	}
	return 0
}

// A set of scenarios to be run with qps_json_driver
type Scenarios struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Scenarios     []*Scenario            `protobuf:"bytes,1,rep,name=scenarios,proto3" json:"scenarios,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Scenarios) Reset() {
	*x = Scenarios{}
	mi := &file_grpc_testing_control_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Scenarios) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Scenarios) ProtoMessage() {}

func (x *Scenarios) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Scenarios.ProtoReflect.Descriptor instead.
func (*Scenarios) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{16}
}

func (x *Scenarios) GetScenarios() []*Scenario {
	if x != nil {
		return x.Scenarios
	}
	return nil
}

// Basic summary that can be computed from ClientStats and ServerStats
// once the scenario has finished.
type ScenarioResultSummary struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Total number of operations per second over all clients. What is counted as 1 'operation' depends on the benchmark scenarios:
	// For unary benchmarks, an operation is processing of a single unary RPC.
	// For streaming benchmarks, an operation is processing of a single ping pong of request and response.
	Qps float64 `protobuf:"fixed64,1,opt,name=qps,proto3" json:"qps,omitempty"`
	// QPS per server core.
	QpsPerServerCore float64 `protobuf:"fixed64,2,opt,name=qps_per_server_core,json=qpsPerServerCore,proto3" json:"qps_per_server_core,omitempty"`
	// The total server cpu load based on system time across all server processes, expressed as percentage of a single cpu core.
	// For example, 85 implies 85% of a cpu core, 125 implies 125% of a cpu core. Since we are accumulating the cpu load across all the server
	// processes, the value could > 100 when there are multiple servers or a single server using multiple threads and cores.
	// Same explanation for the total client cpu load below.
	ServerSystemTime float64 `protobuf:"fixed64,3,opt,name=server_system_time,json=serverSystemTime,proto3" json:"server_system_time,omitempty"`
	// The total server cpu load based on user time across all server processes, expressed as percentage of a single cpu core. (85 => 85%, 125 => 125%)
	ServerUserTime float64 `protobuf:"fixed64,4,opt,name=server_user_time,json=serverUserTime,proto3" json:"server_user_time,omitempty"`
	// The total client cpu load based on system time across all client processes, expressed as percentage of a single cpu core. (85 => 85%, 125 => 125%)
	ClientSystemTime float64 `protobuf:"fixed64,5,opt,name=client_system_time,json=clientSystemTime,proto3" json:"client_system_time,omitempty"`
	// The total client cpu load based on user time across all client processes, expressed as percentage of a single cpu core. (85 => 85%, 125 => 125%)
	ClientUserTime float64 `protobuf:"fixed64,6,opt,name=client_user_time,json=clientUserTime,proto3" json:"client_user_time,omitempty"`
	// X% latency percentiles (in nanoseconds)
	Latency_50  float64 `protobuf:"fixed64,7,opt,name=latency_50,json=latency50,proto3" json:"latency_50,omitempty"`
	Latency_90  float64 `protobuf:"fixed64,8,opt,name=latency_90,json=latency90,proto3" json:"latency_90,omitempty"`
	Latency_95  float64 `protobuf:"fixed64,9,opt,name=latency_95,json=latency95,proto3" json:"latency_95,omitempty"`
	Latency_99  float64 `protobuf:"fixed64,10,opt,name=latency_99,json=latency99,proto3" json:"latency_99,omitempty"`
	Latency_999 float64 `protobuf:"fixed64,11,opt,name=latency_999,json=latency999,proto3" json:"latency_999,omitempty"`
	// server cpu usage percentage
	ServerCpuUsage float64 `protobuf:"fixed64,12,opt,name=server_cpu_usage,json=serverCpuUsage,proto3" json:"server_cpu_usage,omitempty"`
	// Number of requests that succeeded/failed
	SuccessfulRequestsPerSecond float64 `protobuf:"fixed64,13,opt,name=successful_requests_per_second,json=successfulRequestsPerSecond,proto3" json:"successful_requests_per_second,omitempty"`
	FailedRequestsPerSecond     float64 `protobuf:"fixed64,14,opt,name=failed_requests_per_second,json=failedRequestsPerSecond,proto3" json:"failed_requests_per_second,omitempty"`
	// Number of polls called inside completion queue per request
	ClientPollsPerRequest float64 `protobuf:"fixed64,15,opt,name=client_polls_per_request,json=clientPollsPerRequest,proto3" json:"client_polls_per_request,omitempty"`
	ServerPollsPerRequest float64 `protobuf:"fixed64,16,opt,name=server_polls_per_request,json=serverPollsPerRequest,proto3" json:"server_polls_per_request,omitempty"`
	// Queries per CPU-sec over all servers or clients
	ServerQueriesPerCpuSec float64 `protobuf:"fixed64,17,opt,name=server_queries_per_cpu_sec,json=serverQueriesPerCpuSec,proto3" json:"server_queries_per_cpu_sec,omitempty"`
	ClientQueriesPerCpuSec float64 `protobuf:"fixed64,18,opt,name=client_queries_per_cpu_sec,json=clientQueriesPerCpuSec,proto3" json:"client_queries_per_cpu_sec,omitempty"`
	// Start and end time for the test scenario
	StartTime     *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScenarioResultSummary) Reset() {
	*x = ScenarioResultSummary{}
	mi := &file_grpc_testing_control_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScenarioResultSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScenarioResultSummary) ProtoMessage() {}

func (x *ScenarioResultSummary) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScenarioResultSummary.ProtoReflect.Descriptor instead.
func (*ScenarioResultSummary) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{17}
}

func (x *ScenarioResultSummary) GetQps() float64 {
	if x != nil {
		return x.Qps
	}
	return 0
}

func (x *ScenarioResultSummary) GetQpsPerServerCore() float64 {
	if x != nil {
		return x.QpsPerServerCore
	}
	return 0
}

func (x *ScenarioResultSummary) GetServerSystemTime() float64 {
	if x != nil {
		return x.ServerSystemTime
	}
	return 0
}

func (x *ScenarioResultSummary) GetServerUserTime() float64 {
	if x != nil {
		return x.ServerUserTime
	}
	return 0
}

func (x *ScenarioResultSummary) GetClientSystemTime() float64 {
	if x != nil {
		return x.ClientSystemTime
	}
	return 0
}

func (x *ScenarioResultSummary) GetClientUserTime() float64 {
	if x != nil {
		return x.ClientUserTime
	}
	return 0
}

func (x *ScenarioResultSummary) GetLatency_50() float64 {
	if x != nil {
		return x.Latency_50
	}
	return 0
}

func (x *ScenarioResultSummary) GetLatency_90() float64 {
	if x != nil {
		return x.Latency_90
	}
	return 0
}

func (x *ScenarioResultSummary) GetLatency_95() float64 {
	if x != nil {
		return x.Latency_95
	}
	return 0
}

func (x *ScenarioResultSummary) GetLatency_99() float64 {
	if x != nil {
		return x.Latency_99
	}
	return 0
}

func (x *ScenarioResultSummary) GetLatency_999() float64 {
	if x != nil {
		return x.Latency_999
	}
	return 0
}

func (x *ScenarioResultSummary) GetServerCpuUsage() float64 {
	if x != nil {
		return x.ServerCpuUsage
	}
	return 0
}

func (x *ScenarioResultSummary) GetSuccessfulRequestsPerSecond() float64 {
	if x != nil {
		return x.SuccessfulRequestsPerSecond
	}
	return 0
}

func (x *ScenarioResultSummary) GetFailedRequestsPerSecond() float64 {
	if x != nil {
		return x.FailedRequestsPerSecond
	}
	return 0
}

func (x *ScenarioResultSummary) GetClientPollsPerRequest() float64 {
	if x != nil {
		return x.ClientPollsPerRequest
	}
	return 0
}

func (x *ScenarioResultSummary) GetServerPollsPerRequest() float64 {
	if x != nil {
		return x.ServerPollsPerRequest
	}
	return 0
}

func (x *ScenarioResultSummary) GetServerQueriesPerCpuSec() float64 {
	if x != nil {
		return x.ServerQueriesPerCpuSec
	}
	return 0
}

func (x *ScenarioResultSummary) GetClientQueriesPerCpuSec() float64 {
	if x != nil {
		return x.ClientQueriesPerCpuSec
	}
	return 0
}

func (x *ScenarioResultSummary) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ScenarioResultSummary) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

// Results of a single benchmark scenario.
type ScenarioResult struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Inputs used to run the scenario.
	Scenario *Scenario `protobuf:"bytes,1,opt,name=scenario,proto3" json:"scenario,omitempty"`
	// Histograms from all clients merged into one histogram.
	Latencies *HistogramData `protobuf:"bytes,2,opt,name=latencies,proto3" json:"latencies,omitempty"`
	// Client stats for each client
	ClientStats []*ClientStats `protobuf:"bytes,3,rep,name=client_stats,json=clientStats,proto3" json:"client_stats,omitempty"`
	// Server stats for each server
	ServerStats []*ServerStats `protobuf:"bytes,4,rep,name=server_stats,json=serverStats,proto3" json:"server_stats,omitempty"`
	// Number of cores available to each server
	ServerCores []int32 `protobuf:"varint,5,rep,packed,name=server_cores,json=serverCores,proto3" json:"server_cores,omitempty"`
	// An after-the-fact computed summary
	Summary *ScenarioResultSummary `protobuf:"bytes,6,opt,name=summary,proto3" json:"summary,omitempty"`
	// Information on success or failure of each worker
	ClientSuccess []bool `protobuf:"varint,7,rep,packed,name=client_success,json=clientSuccess,proto3" json:"client_success,omitempty"`
	ServerSuccess []bool `protobuf:"varint,8,rep,packed,name=server_success,json=serverSuccess,proto3" json:"server_success,omitempty"`
	// Number of failed requests (one row per status code seen)
	RequestResults []*RequestResultCount `protobuf:"bytes,9,rep,name=request_results,json=requestResults,proto3" json:"request_results,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ScenarioResult) Reset() {
	*x = ScenarioResult{}
	mi := &file_grpc_testing_control_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScenarioResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScenarioResult) ProtoMessage() {}

func (x *ScenarioResult) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_testing_control_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScenarioResult.ProtoReflect.Descriptor instead.
func (*ScenarioResult) Descriptor() ([]byte, []int) {
	return file_grpc_testing_control_proto_rawDescGZIP(), []int{18}
}

func (x *ScenarioResult) GetScenario() *Scenario {
	if x != nil {
		return x.Scenario
	}
	return nil
}

func (x *ScenarioResult) GetLatencies() *HistogramData {
	if x != nil {
		return x.Latencies
	}
	return nil
}

func (x *ScenarioResult) GetClientStats() []*ClientStats {
	if x != nil {
		return x.ClientStats
	}
	return nil
}

func (x *ScenarioResult) GetServerStats() []*ServerStats {
	if x != nil {
		return x.ServerStats
	}
	return nil
}

func (x *ScenarioResult) GetServerCores() []int32 {
	if x != nil {
		return x.ServerCores
	}
	return nil
}

func (x *ScenarioResult) GetSummary() *ScenarioResultSummary {
	if x != nil {
		return x.Summary
	}
	return nil
}

func (x *ScenarioResult) GetClientSuccess() []bool {
	if x != nil {
		return x.ClientSuccess
	}
	return nil
}

func (x *ScenarioResult) GetServerSuccess() []bool {
	if x != nil {
		return x.ServerSuccess
	}
	return nil
}

func (x *ScenarioResult) GetRequestResults() []*RequestResultCount {
	if x != nil {
		return x.RequestResults
	}
	return nil
}

var File_grpc_testing_control_proto protoreflect.FileDescriptor

const file_grpc_testing_control_proto_rawDesc = "" +
	"\n" +
	"\x1agrpc/testing/control.proto\x12\fgrpc.testing\x1a\x1bgrpc/testing/payloads.proto\x1a\x18grpc/testing/stats.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"2\n" +
	"\rPoissonParams\x12!\n" +
	"\foffered_load\x18\x01 \x01(\x01R\vofferedLoad\"\x12\n" +
	"\x10ClosedLoopParams\"\x90\x01\n" +
	"\n" +
	"LoadParams\x12A\n" +
	"\vclosed_loop\x18\x01 \x01(\v2\x1e.grpc.testing.ClosedLoopParamsH\x00R\n" +
	"closedLoop\x127\n" +
	"\apoisson\x18\x02 \x01(\v2\x1b.grpc.testing.PoissonParamsH\x00R\apoissonB\x06\n" +
	"\x04load\"\x7f\n" +
	"\x0eSecurityParams\x12\x1e\n" +
	"\vuse_test_ca\x18\x01 \x01(\bR\tuseTestCa\x120\n" +
	"\x14server_host_override\x18\x02 \x01(\tR\x12serverHostOverride\x12\x1b\n" +
	"\tcred_type\x18\x03 \x01(\tR\bcredType\"g\n" +
	"\n" +
	"ChannelArg\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1d\n" +
	"\tstr_value\x18\x02 \x01(\tH\x00R\bstrValue\x12\x1d\n" +
	"\tint_value\x18\x03 \x01(\x05H\x00R\bintValueB\a\n" +
	"\x05value\"\xf6\a\n" +
	"\fClientConfig\x12%\n" +
	"\x0eserver_targets\x18\x01 \x03(\tR\rserverTargets\x129\n" +
	"\vclient_type\x18\x02 \x01(\x0e2\x18.grpc.testing.ClientTypeR\n" +
	"clientType\x12E\n" +
	"\x0fsecurity_params\x18\x03 \x01(\v2\x1c.grpc.testing.SecurityParamsR\x0esecurityParams\x12?\n" +
	"\x1coutstanding_rpcs_per_channel\x18\x04 \x01(\x05R\x19outstandingRpcsPerChannel\x12'\n" +
	"\x0fclient_channels\x18\x05 \x01(\x05R\x0eclientChannels\x120\n" +
	"\x14async_client_threads\x18\a \x01(\x05R\x12asyncClientThreads\x120\n" +
	"\brpc_type\x18\b \x01(\x0e2\x15.grpc.testing.RpcTypeR\arpcType\x129\n" +
	"\vload_params\x18\n" +
	" \x01(\v2\x18.grpc.testing.LoadParamsR\n" +
	"loadParams\x12B\n" +
	"\x0epayload_config\x18\v \x01(\v2\x1b.grpc.testing.PayloadConfigR\rpayloadConfig\x12H\n" +
	"\x10histogram_params\x18\f \x01(\v2\x1d.grpc.testing.HistogramParamsR\x0fhistogramParams\x12\x1b\n" +
	"\tcore_list\x18\r \x03(\x05R\bcoreList\x12\x1d\n" +
	"\n" +
	"core_limit\x18\x0e \x01(\x05R\tcoreLimit\x12(\n" +
	"\x10other_client_api\x18\x0f \x01(\tR\x0eotherClientApi\x12;\n" +
	"\fchannel_args\x18\x10 \x03(\v2\x18.grpc.testing.ChannelArgR\vchannelArgs\x12$\n" +
	"\x0ethreads_per_cq\x18\x11 \x01(\x05R\fthreadsPerCq\x12.\n" +
	"\x13messages_per_stream\x18\x12 \x01(\x05R\x11messagesPerStream\x12(\n" +
	"\x10use_coalesce_api\x18\x13 \x01(\bR\x0euseCoalesceApi\x12X\n" +
	")median_latency_collection_interval_millis\x18\x14 \x01(\x05R%medianLatencyCollectionIntervalMillis\x12)\n" +
	"\x10client_processes\x18\x15 \x01(\x05R\x0fclientProcesses\"?\n" +
	"\fClientStatus\x12/\n" +
	"\x05stats\x18\x01 \x01(\v2\x19.grpc.testing.ClientStatsR\x05stats\"\x1c\n" +
	"\x04Mark\x12\x14\n" +
	"\x05reset\x18\x01 \x01(\bR\x05reset\"u\n" +
	"\n" +
	"ClientArgs\x122\n" +
	"\x05setup\x18\x01 \x01(\v2\x1a.grpc.testing.ClientConfigH\x00R\x05setup\x12(\n" +
	"\x04mark\x18\x02 \x01(\v2\x12.grpc.testing.MarkH\x00R\x04markB\t\n" +
	"\aargtype\"\xc0\x04\n" +
	"\fServerConfig\x129\n" +
	"\vserver_type\x18\x01 \x01(\x0e2\x18.grpc.testing.ServerTypeR\n" +
	"serverType\x12E\n" +
	"\x0fsecurity_params\x18\x02 \x01(\v2\x1c.grpc.testing.SecurityParamsR\x0esecurityParams\x12\x12\n" +
	"\x04port\x18\x04 \x01(\x05R\x04port\x120\n" +
	"\x14async_server_threads\x18\a \x01(\x05R\x12asyncServerThreads\x12\x1d\n" +
	"\n" +
	"core_limit\x18\b \x01(\x05R\tcoreLimit\x12B\n" +
	"\x0epayload_config\x18\t \x01(\v2\x1b.grpc.testing.PayloadConfigR\rpayloadConfig\x12\x1b\n" +
	"\tcore_list\x18\n" +
	" \x03(\x05R\bcoreList\x12(\n" +
	"\x10other_server_api\x18\v \x01(\tR\x0eotherServerApi\x12$\n" +
	"\x0ethreads_per_cq\x18\f \x01(\x05R\fthreadsPerCq\x12/\n" +
	"\x13resource_quota_size\x18\xe9\a \x01(\x05R\x11resourceQuotaSize\x12<\n" +
	"\fchannel_args\x18\xea\a \x03(\v2\x18.grpc.testing.ChannelArgR\vchannelArgs\x12)\n" +
	"\x10server_processes\x18\x15 \x01(\x05R\x0fserverProcesses\"u\n" +
	"\n" +
	"ServerArgs\x122\n" +
	"\x05setup\x18\x01 \x01(\v2\x1a.grpc.testing.ServerConfigH\x00R\x05setup\x12(\n" +
	"\x04mark\x18\x02 \x01(\v2\x12.grpc.testing.MarkH\x00R\x04markB\t\n" +
	"\aargtype\"i\n" +
	"\fServerStatus\x12/\n" +
	"\x05stats\x18\x01 \x01(\v2\x19.grpc.testing.ServerStatsR\x05stats\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x05R\x04port\x12\x14\n" +
	"\x05cores\x18\x03 \x01(\x05R\x05cores\"\r\n" +
	"\vCoreRequest\"$\n" +
	"\fCoreResponse\x12\x14\n" +
	"\x05cores\x18\x01 \x01(\x05R\x05cores\"\x06\n" +
	"\x04Void\"\xef\x02\n" +
	"\bScenario\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12?\n" +
	"\rclient_config\x18\x02 \x01(\v2\x1a.grpc.testing.ClientConfigR\fclientConfig\x12\x1f\n" +
	"\vnum_clients\x18\x03 \x01(\x05R\n" +
	"numClients\x12?\n" +
	"\rserver_config\x18\x04 \x01(\v2\x1a.grpc.testing.ServerConfigR\fserverConfig\x12\x1f\n" +
	"\vnum_servers\x18\x05 \x01(\x05R\n" +
	"numServers\x12%\n" +
	"\x0ewarmup_seconds\x18\x06 \x01(\x05R\rwarmupSeconds\x12+\n" +
	"\x11benchmark_seconds\x18\a \x01(\x05R\x10benchmarkSeconds\x127\n" +
	"\x18spawn_local_worker_count\x18\b \x01(\x05R\x15spawnLocalWorkerCount\"A\n" +
	"\tScenarios\x124\n" +
	"\tscenarios\x18\x01 \x03(\v2\x16.grpc.testing.ScenarioR\tscenarios\"\xad\a\n" +
	"\x15ScenarioResultSummary\x12\x10\n" +
	"\x03qps\x18\x01 \x01(\x01R\x03qps\x12-\n" +
	"\x13qps_per_server_core\x18\x02 \x01(\x01R\x10qpsPerServerCore\x12,\n" +
	"\x12server_system_time\x18\x03 \x01(\x01R\x10serverSystemTime\x12(\n" +
	"\x10server_user_time\x18\x04 \x01(\x01R\x0eserverUserTime\x12,\n" +
	"\x12client_system_time\x18\x05 \x01(\x01R\x10clientSystemTime\x12(\n" +
	"\x10client_user_time\x18\x06 \x01(\x01R\x0eclientUserTime\x12\x1d\n" +
	"\n" +
	"latency_50\x18\a \x01(\x01R\tlatency50\x12\x1d\n" +
	"\n" +
	"latency_90\x18\b \x01(\x01R\tlatency90\x12\x1d\n" +
	"\n" +
	"latency_95\x18\t \x01(\x01R\tlatency95\x12\x1d\n" +
	"\n" +
	"latency_99\x18\n" +
	" \x01(\x01R\tlatency99\x12\x1f\n" +
	"\vlatency_999\x18\v \x01(\x01R\n" +
	"latency999\x12(\n" +
	"\x10server_cpu_usage\x18\f \x01(\x01R\x0eserverCpuUsage\x12C\n" +
	"\x1esuccessful_requests_per_second\x18\r \x01(\x01R\x1bsuccessfulRequestsPerSecond\x12;\n" +
	"\x1afailed_requests_per_second\x18\x0e \x01(\x01R\x17failedRequestsPerSecond\x127\n" +
	"\x18client_polls_per_request\x18\x0f \x01(\x01R\x15clientPollsPerRequest\x127\n" +
	"\x18server_polls_per_request\x18\x10 \x01(\x01R\x15serverPollsPerRequest\x12:\n" +
	"\x1aserver_queries_per_cpu_sec\x18\x11 \x01(\x01R\x16serverQueriesPerCpuSec\x12:\n" +
	"\x1aclient_queries_per_cpu_sec\x18\x12 \x01(\x01R\x16clientQueriesPerCpuSec\x129\n" +
	"\n" +
	"start_time\x18\x13 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x14 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\"\xf6\x03\n" +
	"\x0eScenarioResult\x122\n" +
	"\bscenario\x18\x01 \x01(\v2\x16.grpc.testing.ScenarioR\bscenario\x129\n" +
	"\tlatencies\x18\x02 \x01(\v2\x1b.grpc.testing.HistogramDataR\tlatencies\x12<\n" +
	"\fclient_stats\x18\x03 \x03(\v2\x19.grpc.testing.ClientStatsR\vclientStats\x12<\n" +
	"\fserver_stats\x18\x04 \x03(\v2\x19.grpc.testing.ServerStatsR\vserverStats\x12!\n" +
	"\fserver_cores\x18\x05 \x03(\x05R\vserverCores\x12=\n" +
	"\asummary\x18\x06 \x01(\v2#.grpc.testing.ScenarioResultSummaryR\asummary\x12%\n" +
	"\x0eclient_success\x18\a \x03(\bR\rclientSuccess\x12%\n" +
	"\x0eserver_success\x18\b \x03(\bR\rserverSuccess\x12I\n" +
	"\x0frequest_results\x18\t \x03(\v2 .grpc.testing.RequestResultCountR\x0erequestResults*V\n" +
	"\n" +
	"ClientType\x12\x0f\n" +
	"\vSYNC_CLIENT\x10\x00\x12\x10\n" +
	"\fASYNC_CLIENT\x10\x01\x12\x10\n" +
	"\fOTHER_CLIENT\x10\x02\x12\x13\n" +
	"\x0fCALLBACK_CLIENT\x10\x03*p\n" +
	"\n" +
	"ServerType\x12\x0f\n" +
	"\vSYNC_SERVER\x10\x00\x12\x10\n" +
	"\fASYNC_SERVER\x10\x01\x12\x18\n" +
	"\x14ASYNC_GENERIC_SERVER\x10\x02\x12\x10\n" +
	"\fOTHER_SERVER\x10\x03\x12\x13\n" +
	"\x0fCALLBACK_SERVER\x10\x04*r\n" +
	"\aRpcType\x12\t\n" +
	"\x05UNARY\x10\x00\x12\r\n" +
	"\tSTREAMING\x10\x01\x12\x19\n" +
	"\x15STREAMING_FROM_CLIENT\x10\x02\x12\x19\n" +
	"\x15STREAMING_FROM_SERVER\x10\x03\x12\x17\n" +
	"\x13STREAMING_BOTH_WAYS\x10\x04B!\n" +
	"\x0fio.grpc.testingB\fControlProtoP\x01b\x06proto3"

var (
	file_grpc_testing_control_proto_rawDescOnce sync.Once
	file_grpc_testing_control_proto_rawDescData []byte
)

func file_grpc_testing_control_proto_rawDescGZIP() []byte {
	file_grpc_testing_control_proto_rawDescOnce.Do(func() {
		file_grpc_testing_control_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_grpc_testing_control_proto_rawDesc), len(file_grpc_testing_control_proto_rawDesc)))
	})
	return file_grpc_testing_control_proto_rawDescData
}

var file_grpc_testing_control_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_grpc_testing_control_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_grpc_testing_control_proto_goTypes = []any{
	(ClientType)(0),               // 0: grpc.testing.ClientType
	(ServerType)(0),               // 1: grpc.testing.ServerType
	(RpcType)(0),                  // 2: grpc.testing.RpcType
	(*PoissonParams)(nil),         // 3: grpc.testing.PoissonParams
	(*ClosedLoopParams)(nil),      // 4: grpc.testing.ClosedLoopParams
	(*LoadParams)(nil),            // 5: grpc.testing.LoadParams
	(*SecurityParams)(nil),        // 6: grpc.testing.SecurityParams
	(*ChannelArg)(nil),            // 7: grpc.testing.ChannelArg
	(*ClientConfig)(nil),          // 8: grpc.testing.ClientConfig
	(*ClientStatus)(nil),          // 9: grpc.testing.ClientStatus
	(*Mark)(nil),                  // 10: grpc.testing.Mark
	(*ClientArgs)(nil),            // 11: grpc.testing.ClientArgs
	(*ServerConfig)(nil),          // 12: grpc.testing.ServerConfig
	(*ServerArgs)(nil),            // 13: grpc.testing.ServerArgs
	(*ServerStatus)(nil),          // 14: grpc.testing.ServerStatus
	(*CoreRequest)(nil),           // 15: grpc.testing.CoreRequest
	(*CoreResponse)(nil),          // 16: grpc.testing.CoreResponse
	(*Void)(nil),                  // 17: grpc.testing.Void
	(*Scenario)(nil),              // 18: grpc.testing.Scenario
	(*Scenarios)(nil),             // 19: grpc.testing.Scenarios
	(*ScenarioResultSummary)(nil), // 20: grpc.testing.ScenarioResultSummary
	(*ScenarioResult)(nil),        // 21: grpc.testing.ScenarioResult
	(*PayloadConfig)(nil),         // 22: grpc.testing.PayloadConfig
	(*HistogramParams)(nil),       // 23: grpc.testing.HistogramParams
	(*ClientStats)(nil),           // 24: grpc.testing.ClientStats
	(*ServerStats)(nil),           // 25: grpc.testing.ServerStats
	(*timestamppb.Timestamp)(nil), // 26: google.protobuf.Timestamp
	(*HistogramData)(nil),         // 27: grpc.testing.HistogramData
	(*RequestResultCount)(nil),    // 28: grpc.testing.RequestResultCount
}
var file_grpc_testing_control_proto_depIdxs = []int32{
	4,  // 0: grpc.testing.LoadParams.closed_loop:type_name -> grpc.testing.ClosedLoopParams
	3,  // 1: grpc.testing.LoadParams.poisson:type_name -> grpc.testing.PoissonParams
	0,  // 2: grpc.testing.ClientConfig.client_type:type_name -> grpc.testing.ClientType
	6,  // 3: grpc.testing.ClientConfig.security_params:type_name -> grpc.testing.SecurityParams
	2,  // 4: grpc.testing.ClientConfig.rpc_type:type_name -> grpc.testing.RpcType
	5,  // 5: grpc.testing.ClientConfig.load_params:type_name -> grpc.testing.LoadParams
	22, // 6: grpc.testing.ClientConfig.payload_config:type_name -> grpc.testing.PayloadConfig
	23, // 7: grpc.testing.ClientConfig.histogram_params:type_name -> grpc.testing.HistogramParams
	7,  // 8: grpc.testing.ClientConfig.channel_args:type_name -> grpc.testing.ChannelArg
	24, // 9: grpc.testing.ClientStatus.stats:type_name -> grpc.testing.ClientStats
	8,  // 10: grpc.testing.ClientArgs.setup:type_name -> grpc.testing.ClientConfig
	10, // 11: grpc.testing.ClientArgs.mark:type_name -> grpc.testing.Mark
	1,  // 12: grpc.testing.ServerConfig.server_type:type_name -> grpc.testing.ServerType
	6,  // 13: grpc.testing.ServerConfig.security_params:type_name -> grpc.testing.SecurityParams
	22, // 14: grpc.testing.ServerConfig.payload_config:type_name -> grpc.testing.PayloadConfig
	7,  // 15: grpc.testing.ServerConfig.channel_args:type_name -> grpc.testing.ChannelArg
	12, // 16: grpc.testing.ServerArgs.setup:type_name -> grpc.testing.ServerConfig
	10, // 17: grpc.testing.ServerArgs.mark:type_name -> grpc.testing.Mark
	25, // 18: grpc.testing.ServerStatus.stats:type_name -> grpc.testing.ServerStats
	8,  // 19: grpc.testing.Scenario.client_config:type_name -> grpc.testing.ClientConfig
	12, // 20: grpc.testing.Scenario.server_config:type_name -> grpc.testing.ServerConfig
	18, // 21: grpc.testing.Scenarios.scenarios:type_name -> grpc.testing.Scenario
	26, // 22: grpc.testing.ScenarioResultSummary.start_time:type_name -> google.protobuf.Timestamp
	26, // 23: grpc.testing.ScenarioResultSummary.end_time:type_name -> google.protobuf.Timestamp
	18, // 24: grpc.testing.ScenarioResult.scenario:type_name -> grpc.testing.Scenario
	27, // 25: grpc.testing.ScenarioResult.latencies:type_name -> grpc.testing.HistogramData
	24, // 26: grpc.testing.ScenarioResult.client_stats:type_name -> grpc.testing.ClientStats
	25, // 27: grpc.testing.ScenarioResult.server_stats:type_name -> grpc.testing.ServerStats
	20, // 28: grpc.testing.ScenarioResult.summary:type_name -> grpc.testing.ScenarioResultSummary
	28, // 29: grpc.testing.ScenarioResult.request_results:type_name -> grpc.testing.RequestResultCount
	30, // [30:30] is the sub-list for method output_type
	30, // [30:30] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_grpc_testing_control_proto_init() }
func file_grpc_testing_control_proto_init() {
	if File_grpc_testing_control_proto != nil {
		return
	}
	file_grpc_testing_payloads_proto_init()
	file_grpc_testing_stats_proto_init()
	file_grpc_testing_control_proto_msgTypes[2].OneofWrappers = []any{
		(*LoadParams_ClosedLoop)(nil),
		(*LoadParams_Poisson)(nil),
	}
	file_grpc_testing_control_proto_msgTypes[4].OneofWrappers = []any{
		(*ChannelArg_StrValue)(nil),
		(*ChannelArg_IntValue)(nil),
	}
	file_grpc_testing_control_proto_msgTypes[8].OneofWrappers = []any{
		(*ClientArgs_Setup)(nil),
		(*ClientArgs_Mark)(nil),
	}
	file_grpc_testing_control_proto_msgTypes[10].OneofWrappers = []any{
		(*ServerArgs_Setup)(nil),
		(*ServerArgs_Mark)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_grpc_testing_control_proto_rawDesc), len(file_grpc_testing_control_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_grpc_testing_control_proto_goTypes,
		DependencyIndexes: file_grpc_testing_control_proto_depIdxs,
		EnumInfos:         file_grpc_testing_control_proto_enumTypes,
		MessageInfos:      file_grpc_testing_control_proto_msgTypes,
	}.Build()
	File_grpc_testing_control_proto = out.File
	file_grpc_testing_control_proto_goTypes = nil
	file_grpc_testing_control_proto_depIdxs = nil
}
