/*
 *
 * Copyright 2018 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

// This binary can only run on Google Cloud Platform (GCP).
package main

import (
	"context"
	"flag"
	"net"
	"strings"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/alts"
	"google.golang.org/grpc/grpclog"
	"google.golang.org/grpc/interop"
	"google.golang.org/grpc/tap"

	testgrpc "google.golang.org/grpc/interop/grpc_testing"
)

const (
	udsAddrPrefix = "unix:"
)

var (
	hsAddr     = flag.String("alts_handshaker_service_address", "", "ALTS handshaker gRPC service address")
	serverAddr = flag.String("server_address", ":8080", "The address on which the server is listening. Only two types of addresses are supported, 'host:port' and 'unix:/path'.")

	logger = grpclog.Component("interop")
)

func main() {
	flag.Parse()

	// If the server address starts with `unix:`, then we have a UDS address.
	network := "tcp"
	address := *serverAddr
	if strings.HasPrefix(address, udsAddrPrefix) {
		network = "unix"
		address = strings.TrimPrefix(address, udsAddrPrefix)
	}
	lis, err := net.Listen(network, address)
	if err != nil {
		logger.Fatalf("gRPC Server: failed to start the server at %v: %v", address, err)
	}
	opts := alts.DefaultServerOptions()
	if *hsAddr != "" {
		opts.HandshakerServiceAddress = *hsAddr
	}
	altsTC := alts.NewServerCreds(opts)
	grpcServer := grpc.NewServer(grpc.Creds(altsTC), grpc.InTapHandle(authz))
	testgrpc.RegisterTestServiceServer(grpcServer, interop.NewTestServer())
	grpcServer.Serve(lis)
}

// authz shows how to access client information at the server side to perform
// application-layer authorization checks.
func authz(ctx context.Context, info *tap.Info) (context.Context, error) {
	authInfo, err := alts.AuthInfoFromContext(ctx)
	if err != nil {
		return nil, err
	}
	// Access all alts.AuthInfo data:
	logger.Infof("authInfo.ApplicationProtocol() = %v", authInfo.ApplicationProtocol())
	logger.Infof("authInfo.RecordProtocol() = %v", authInfo.RecordProtocol())
	logger.Infof("authInfo.SecurityLevel() = %v", authInfo.SecurityLevel())
	logger.Infof("authInfo.PeerServiceAccount() = %v", authInfo.PeerServiceAccount())
	logger.Infof("authInfo.LocalServiceAccount() = %v", authInfo.LocalServiceAccount())
	logger.Infof("authInfo.PeerRPCVersions() = %v", authInfo.PeerRPCVersions())
	logger.Infof("info.FullMethodName = %v", info.FullMethodName)
	return ctx, nil
}
