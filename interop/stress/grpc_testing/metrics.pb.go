// Copyright 2015-2016 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Contains the definitions for a metrics service and the type of metrics
// exposed by the service.
//
// Currently, 'Gauge' (i.e a metric that represents the measured value of
// something at an instant of time) is the only metric type supported by the
// service.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: interop/stress/grpc_testing/metrics.proto

package grpc_testing

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Response message containing the gauge name and value
type GaugeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Name  string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Types that are valid to be assigned to Value:
	//
	//	*GaugeResponse_LongValue
	//	*GaugeResponse_DoubleValue
	//	*GaugeResponse_StringValue
	Value         isGaugeResponse_Value `protobuf_oneof:"value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GaugeResponse) Reset() {
	*x = GaugeResponse{}
	mi := &file_interop_stress_grpc_testing_metrics_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GaugeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GaugeResponse) ProtoMessage() {}

func (x *GaugeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_interop_stress_grpc_testing_metrics_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GaugeResponse.ProtoReflect.Descriptor instead.
func (*GaugeResponse) Descriptor() ([]byte, []int) {
	return file_interop_stress_grpc_testing_metrics_proto_rawDescGZIP(), []int{0}
}

func (x *GaugeResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GaugeResponse) GetValue() isGaugeResponse_Value {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *GaugeResponse) GetLongValue() int64 {
	if x != nil {
		if x, ok := x.Value.(*GaugeResponse_LongValue); ok {
			return x.LongValue
		}
	}
	return 0
}

func (x *GaugeResponse) GetDoubleValue() float64 {
	if x != nil {
		if x, ok := x.Value.(*GaugeResponse_DoubleValue); ok {
			return x.DoubleValue
		}
	}
	return 0
}

func (x *GaugeResponse) GetStringValue() string {
	if x != nil {
		if x, ok := x.Value.(*GaugeResponse_StringValue); ok {
			return x.StringValue
		}
	}
	return ""
}

type isGaugeResponse_Value interface {
	isGaugeResponse_Value()
}

type GaugeResponse_LongValue struct {
	LongValue int64 `protobuf:"varint,2,opt,name=long_value,json=longValue,proto3,oneof"`
}

type GaugeResponse_DoubleValue struct {
	DoubleValue float64 `protobuf:"fixed64,3,opt,name=double_value,json=doubleValue,proto3,oneof"`
}

type GaugeResponse_StringValue struct {
	StringValue string `protobuf:"bytes,4,opt,name=string_value,json=stringValue,proto3,oneof"`
}

func (*GaugeResponse_LongValue) isGaugeResponse_Value() {}

func (*GaugeResponse_DoubleValue) isGaugeResponse_Value() {}

func (*GaugeResponse_StringValue) isGaugeResponse_Value() {}

// Request message containing the gauge name
type GaugeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GaugeRequest) Reset() {
	*x = GaugeRequest{}
	mi := &file_interop_stress_grpc_testing_metrics_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GaugeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GaugeRequest) ProtoMessage() {}

func (x *GaugeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_interop_stress_grpc_testing_metrics_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GaugeRequest.ProtoReflect.Descriptor instead.
func (*GaugeRequest) Descriptor() ([]byte, []int) {
	return file_interop_stress_grpc_testing_metrics_proto_rawDescGZIP(), []int{1}
}

func (x *GaugeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type EmptyMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmptyMessage) Reset() {
	*x = EmptyMessage{}
	mi := &file_interop_stress_grpc_testing_metrics_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmptyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyMessage) ProtoMessage() {}

func (x *EmptyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_interop_stress_grpc_testing_metrics_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyMessage.ProtoReflect.Descriptor instead.
func (*EmptyMessage) Descriptor() ([]byte, []int) {
	return file_interop_stress_grpc_testing_metrics_proto_rawDescGZIP(), []int{2}
}

var File_interop_stress_grpc_testing_metrics_proto protoreflect.FileDescriptor

const file_interop_stress_grpc_testing_metrics_proto_rawDesc = "" +
	"\n" +
	")interop/stress/grpc_testing/metrics.proto\x12\fgrpc.testing\"\x97\x01\n" +
	"\rGaugeResponse\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1f\n" +
	"\n" +
	"long_value\x18\x02 \x01(\x03H\x00R\tlongValue\x12#\n" +
	"\fdouble_value\x18\x03 \x01(\x01H\x00R\vdoubleValue\x12#\n" +
	"\fstring_value\x18\x04 \x01(\tH\x00R\vstringValueB\a\n" +
	"\x05value\"\"\n" +
	"\fGaugeRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"\x0e\n" +
	"\fEmptyMessage2\xa0\x01\n" +
	"\x0eMetricsService\x12I\n" +
	"\fGetAllGauges\x12\x1a.grpc.testing.EmptyMessage\x1a\x1b.grpc.testing.GaugeResponse0\x01\x12C\n" +
	"\bGetGauge\x12\x1a.grpc.testing.GaugeRequest\x1a\x1b.grpc.testing.GaugeResponseB4Z2google.golang.org/grpc/interop/stress/grpc_testingb\x06proto3"

var (
	file_interop_stress_grpc_testing_metrics_proto_rawDescOnce sync.Once
	file_interop_stress_grpc_testing_metrics_proto_rawDescData []byte
)

func file_interop_stress_grpc_testing_metrics_proto_rawDescGZIP() []byte {
	file_interop_stress_grpc_testing_metrics_proto_rawDescOnce.Do(func() {
		file_interop_stress_grpc_testing_metrics_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_interop_stress_grpc_testing_metrics_proto_rawDesc), len(file_interop_stress_grpc_testing_metrics_proto_rawDesc)))
	})
	return file_interop_stress_grpc_testing_metrics_proto_rawDescData
}

var file_interop_stress_grpc_testing_metrics_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_interop_stress_grpc_testing_metrics_proto_goTypes = []any{
	(*GaugeResponse)(nil), // 0: grpc.testing.GaugeResponse
	(*GaugeRequest)(nil),  // 1: grpc.testing.GaugeRequest
	(*EmptyMessage)(nil),  // 2: grpc.testing.EmptyMessage
}
var file_interop_stress_grpc_testing_metrics_proto_depIdxs = []int32{
	2, // 0: grpc.testing.MetricsService.GetAllGauges:input_type -> grpc.testing.EmptyMessage
	1, // 1: grpc.testing.MetricsService.GetGauge:input_type -> grpc.testing.GaugeRequest
	0, // 2: grpc.testing.MetricsService.GetAllGauges:output_type -> grpc.testing.GaugeResponse
	0, // 3: grpc.testing.MetricsService.GetGauge:output_type -> grpc.testing.GaugeResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_interop_stress_grpc_testing_metrics_proto_init() }
func file_interop_stress_grpc_testing_metrics_proto_init() {
	if File_interop_stress_grpc_testing_metrics_proto != nil {
		return
	}
	file_interop_stress_grpc_testing_metrics_proto_msgTypes[0].OneofWrappers = []any{
		(*GaugeResponse_LongValue)(nil),
		(*GaugeResponse_DoubleValue)(nil),
		(*GaugeResponse_StringValue)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_interop_stress_grpc_testing_metrics_proto_rawDesc), len(file_interop_stress_grpc_testing_metrics_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_interop_stress_grpc_testing_metrics_proto_goTypes,
		DependencyIndexes: file_interop_stress_grpc_testing_metrics_proto_depIdxs,
		MessageInfos:      file_interop_stress_grpc_testing_metrics_proto_msgTypes,
	}.Build()
	File_interop_stress_grpc_testing_metrics_proto = out.File
	file_interop_stress_grpc_testing_metrics_proto_goTypes = nil
	file_interop_stress_grpc_testing_metrics_proto_depIdxs = nil
}
