package main

import (
	"context"
	"fmt"
	"log"
	"net"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	pb "google.golang.org/grpc/examples/helloworld/helloworld"
)

// server is used to implement helloworld.GreeterServer.
type server struct {
	pb.UnimplementedGreeterServer
}

// Say<PERSON>ello implements helloworld.GreeterServer
func (s *server) SayHello(ctx context.Context, in *pb.HelloRequest) (*pb.HelloReply, error) {
	fmt.Printf("Received request: %v\n", in.GetName())
	
	// 测试不同的返回情况
	switch in.GetName() {
	case "nil_response":
		// 返回 nil 响应和 nil 错误
		return nil, nil
	case "empty_response":
		// 返回空响应和 nil 错误
		return &pb.HelloReply{}, nil
	case "normal_response":
		// 正常响应
		return &pb.HelloReply{Message: "Hello " + in.GetName()}, nil
	case "error_response":
		// 返回错误
		return nil, status.Errorf(codes.InvalidArgument, "invalid name: %s", in.GetName())
	default:
		return &pb.HelloReply{Message: "Hello " + in.GetName()}, nil
	}
}

func main() {
	lis, err := net.Listen("tcp", ":50051")
	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}
	
	s := grpc.NewServer()
	pb.RegisterGreeterServer(s, &server{})
	
	fmt.Println("Server listening on :50051")
	if err := s.Serve(lis); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
