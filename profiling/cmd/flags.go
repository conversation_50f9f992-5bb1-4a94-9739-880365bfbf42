/*
 *
 * Copyright 2019 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package main

import (
	"flag"
	"fmt"
)

var flagAddress = flag.String("address", "", "address of a remote gRPC server with profiling turned on to retrieve stats from")
var flagTimeout = flag.Int("timeout", 0, "network operations timeout in seconds to remote target (0 indicates unlimited)")

var flagRetrieveSnapshot = flag.Bool("retrieve-snapshot", false, "connect to remote target and retrieve a profiling snapshot locally for processing")
var flagSnapshot = flag.String("snapshot", "", "snapshot file to write to when retrieving profiling data or snapshot file to read from when processing profiling data")

var flagEnableProfiling = flag.Bool("enable-profiling", false, "enable profiling in remote target")
var flagDisableProfiling = flag.Bool("disable-profiling", false, "disable profiling in remote target")

var flagStreamStatsCatapultJSON = flag.String("stream-stats-catapult-json", "", "path to a file to write to after transforming a snapshot into catapult's JSON format")
var flagStreamStatsFilter = flag.String("stream-stats-filter", "server,client", "comma-separated list of stat tags to filter for")

func exactlyOneOf(opts ...bool) bool {
	first := true
	for _, o := range opts {
		if !o {
			continue
		}

		if !first {
			return false
		}
		first = false
	}

	return !first
}

func parseArgs() error {
	flag.Parse()

	if *flagAddress != "" {
		if !exactlyOneOf(*flagEnableProfiling, *flagDisableProfiling, *flagRetrieveSnapshot) {
			return fmt.Errorf("when -address is specified, you must include exactly only one of -enable-profiling, -disable-profiling, and -retrieve-snapshot")
		}

		if *flagStreamStatsCatapultJSON != "" {
			return fmt.Errorf("when -address is specified, you must not include -stream-stats-catapult-json")
		}
	} else {
		if *flagEnableProfiling || *flagDisableProfiling || *flagRetrieveSnapshot {
			return fmt.Errorf("when -address isn't specified, you must not include any of -enable-profiling, -disable-profiling, and -retrieve-snapshot")
		}

		if *flagStreamStatsCatapultJSON == "" {
			return fmt.Errorf("when -address isn't specified, you must include -stream-stats-catapult-json")
		}
	}

	return nil
}
