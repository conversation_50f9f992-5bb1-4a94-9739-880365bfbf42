// Copyright 2019 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: profiling/proto/service.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// EnableRequest defines the fields in a /Profiling/Enable method request to
// toggle profiling on and off within a gRPC program.
type EnableRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Setting this to true will enable profiling. Setting this to false will
	// disable profiling.
	Enabled       bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnableRequest) Reset() {
	*x = EnableRequest{}
	mi := &file_profiling_proto_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableRequest) ProtoMessage() {}

func (x *EnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_profiling_proto_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableRequest.ProtoReflect.Descriptor instead.
func (*EnableRequest) Descriptor() ([]byte, []int) {
	return file_profiling_proto_service_proto_rawDescGZIP(), []int{0}
}

func (x *EnableRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

// EnableResponse defines the fields in a /Profiling/Enable method response.
type EnableResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnableResponse) Reset() {
	*x = EnableResponse{}
	mi := &file_profiling_proto_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnableResponse) ProtoMessage() {}

func (x *EnableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_profiling_proto_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnableResponse.ProtoReflect.Descriptor instead.
func (*EnableResponse) Descriptor() ([]byte, []int) {
	return file_profiling_proto_service_proto_rawDescGZIP(), []int{1}
}

// GetStreamStatsRequest defines the fields in a /Profiling/GetStreamStats
// method request to retrieve stream-level stats in a gRPC client/server.
type GetStreamStatsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetStreamStatsRequest) Reset() {
	*x = GetStreamStatsRequest{}
	mi := &file_profiling_proto_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetStreamStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStreamStatsRequest) ProtoMessage() {}

func (x *GetStreamStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_profiling_proto_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStreamStatsRequest.ProtoReflect.Descriptor instead.
func (*GetStreamStatsRequest) Descriptor() ([]byte, []int) {
	return file_profiling_proto_service_proto_rawDescGZIP(), []int{2}
}

// GetStreamStatsResponse defines the fields in a /Profiling/GetStreamStats
// method response.
type GetStreamStatsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamStats   []*Stat                `protobuf:"bytes,1,rep,name=stream_stats,json=streamStats,proto3" json:"stream_stats,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetStreamStatsResponse) Reset() {
	*x = GetStreamStatsResponse{}
	mi := &file_profiling_proto_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetStreamStatsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStreamStatsResponse) ProtoMessage() {}

func (x *GetStreamStatsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_profiling_proto_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStreamStatsResponse.ProtoReflect.Descriptor instead.
func (*GetStreamStatsResponse) Descriptor() ([]byte, []int) {
	return file_profiling_proto_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetStreamStatsResponse) GetStreamStats() []*Stat {
	if x != nil {
		return x.StreamStats
	}
	return nil
}

// A Timer measures the start and end of execution of a component within
// gRPC that's being profiled. It includes a tag and some additional metadata
// to identify itself.
type Timer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// tags is a comma-separated list of strings used to tag a timer.
	Tags string `protobuf:"bytes,1,opt,name=tags,proto3" json:"tags,omitempty"`
	// begin_sec and begin_nsec are the start epoch second and nanosecond,
	// respectively, of the component profiled by this timer in UTC. begin_nsec
	// must be a non-negative integer.
	BeginSec  int64 `protobuf:"varint,2,opt,name=begin_sec,json=beginSec,proto3" json:"begin_sec,omitempty"`
	BeginNsec int32 `protobuf:"varint,3,opt,name=begin_nsec,json=beginNsec,proto3" json:"begin_nsec,omitempty"`
	// end_sec and end_nsec are the end epoch second and nanosecond,
	// respectively, of the component profiled by this timer in UTC. end_nsec
	// must be a non-negative integer.
	EndSec  int64 `protobuf:"varint,4,opt,name=end_sec,json=endSec,proto3" json:"end_sec,omitempty"`
	EndNsec int32 `protobuf:"varint,5,opt,name=end_nsec,json=endNsec,proto3" json:"end_nsec,omitempty"`
	// go_id is the goroutine ID of the component being profiled.
	GoId          int64 `protobuf:"varint,6,opt,name=go_id,json=goId,proto3" json:"go_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Timer) Reset() {
	*x = Timer{}
	mi := &file_profiling_proto_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Timer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Timer) ProtoMessage() {}

func (x *Timer) ProtoReflect() protoreflect.Message {
	mi := &file_profiling_proto_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Timer.ProtoReflect.Descriptor instead.
func (*Timer) Descriptor() ([]byte, []int) {
	return file_profiling_proto_service_proto_rawDescGZIP(), []int{4}
}

func (x *Timer) GetTags() string {
	if x != nil {
		return x.Tags
	}
	return ""
}

func (x *Timer) GetBeginSec() int64 {
	if x != nil {
		return x.BeginSec
	}
	return 0
}

func (x *Timer) GetBeginNsec() int32 {
	if x != nil {
		return x.BeginNsec
	}
	return 0
}

func (x *Timer) GetEndSec() int64 {
	if x != nil {
		return x.EndSec
	}
	return 0
}

func (x *Timer) GetEndNsec() int32 {
	if x != nil {
		return x.EndNsec
	}
	return 0
}

func (x *Timer) GetGoId() int64 {
	if x != nil {
		return x.GoId
	}
	return 0
}

// A Stat is a collection of Timers along with some additional
// metadata to tag and identify itself.
type Stat struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// tags is a comma-separated list of strings used to categorize a stat.
	Tags string `protobuf:"bytes,1,opt,name=tags,proto3" json:"tags,omitempty"`
	// timers is an array of Timers, each representing a different
	// (but possibly overlapping) component within this stat.
	Timers []*Timer `protobuf:"bytes,2,rep,name=timers,proto3" json:"timers,omitempty"`
	// metadata is an array of bytes used to uniquely identify a stat with an
	// undefined encoding format. For example, the Stats returned by the
	// /Profiling/GetStreamStats service use the metadata field to encode the
	// connection ID and the stream ID of each query.
	Metadata      []byte `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Stat) Reset() {
	*x = Stat{}
	mi := &file_profiling_proto_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Stat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Stat) ProtoMessage() {}

func (x *Stat) ProtoReflect() protoreflect.Message {
	mi := &file_profiling_proto_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Stat.ProtoReflect.Descriptor instead.
func (*Stat) Descriptor() ([]byte, []int) {
	return file_profiling_proto_service_proto_rawDescGZIP(), []int{5}
}

func (x *Stat) GetTags() string {
	if x != nil {
		return x.Tags
	}
	return ""
}

func (x *Stat) GetTimers() []*Timer {
	if x != nil {
		return x.Timers
	}
	return nil
}

func (x *Stat) GetMetadata() []byte {
	if x != nil {
		return x.Metadata
	}
	return nil
}

var File_profiling_proto_service_proto protoreflect.FileDescriptor

const file_profiling_proto_service_proto_rawDesc = "" +
	"\n" +
	"\x1dprofiling/proto/service.proto\x12\x19grpc.go.profiling.v1alpha\")\n" +
	"\rEnableRequest\x12\x18\n" +
	"\aenabled\x18\x01 \x01(\bR\aenabled\"\x10\n" +
	"\x0eEnableResponse\"\x17\n" +
	"\x15GetStreamStatsRequest\"\\\n" +
	"\x16GetStreamStatsResponse\x12B\n" +
	"\fstream_stats\x18\x01 \x03(\v2\x1f.grpc.go.profiling.v1alpha.StatR\vstreamStats\"\xa0\x01\n" +
	"\x05Timer\x12\x12\n" +
	"\x04tags\x18\x01 \x01(\tR\x04tags\x12\x1b\n" +
	"\tbegin_sec\x18\x02 \x01(\x03R\bbeginSec\x12\x1d\n" +
	"\n" +
	"begin_nsec\x18\x03 \x01(\x05R\tbeginNsec\x12\x17\n" +
	"\aend_sec\x18\x04 \x01(\x03R\x06endSec\x12\x19\n" +
	"\bend_nsec\x18\x05 \x01(\x05R\aendNsec\x12\x13\n" +
	"\x05go_id\x18\x06 \x01(\x03R\x04goId\"p\n" +
	"\x04Stat\x12\x12\n" +
	"\x04tags\x18\x01 \x01(\tR\x04tags\x128\n" +
	"\x06timers\x18\x02 \x03(\v2 .grpc.go.profiling.v1alpha.TimerR\x06timers\x12\x1a\n" +
	"\bmetadata\x18\x03 \x01(\fR\bmetadata2\xe1\x01\n" +
	"\tProfiling\x12]\n" +
	"\x06Enable\x12(.grpc.go.profiling.v1alpha.EnableRequest\x1a).grpc.go.profiling.v1alpha.EnableResponse\x12u\n" +
	"\x0eGetStreamStats\x120.grpc.go.profiling.v1alpha.GetStreamStatsRequest\x1a1.grpc.go.profiling.v1alpha.GetStreamStatsResponseB(Z&google.golang.org/grpc/profiling/protob\x06proto3"

var (
	file_profiling_proto_service_proto_rawDescOnce sync.Once
	file_profiling_proto_service_proto_rawDescData []byte
)

func file_profiling_proto_service_proto_rawDescGZIP() []byte {
	file_profiling_proto_service_proto_rawDescOnce.Do(func() {
		file_profiling_proto_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_profiling_proto_service_proto_rawDesc), len(file_profiling_proto_service_proto_rawDesc)))
	})
	return file_profiling_proto_service_proto_rawDescData
}

var file_profiling_proto_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_profiling_proto_service_proto_goTypes = []any{
	(*EnableRequest)(nil),          // 0: grpc.go.profiling.v1alpha.EnableRequest
	(*EnableResponse)(nil),         // 1: grpc.go.profiling.v1alpha.EnableResponse
	(*GetStreamStatsRequest)(nil),  // 2: grpc.go.profiling.v1alpha.GetStreamStatsRequest
	(*GetStreamStatsResponse)(nil), // 3: grpc.go.profiling.v1alpha.GetStreamStatsResponse
	(*Timer)(nil),                  // 4: grpc.go.profiling.v1alpha.Timer
	(*Stat)(nil),                   // 5: grpc.go.profiling.v1alpha.Stat
}
var file_profiling_proto_service_proto_depIdxs = []int32{
	5, // 0: grpc.go.profiling.v1alpha.GetStreamStatsResponse.stream_stats:type_name -> grpc.go.profiling.v1alpha.Stat
	4, // 1: grpc.go.profiling.v1alpha.Stat.timers:type_name -> grpc.go.profiling.v1alpha.Timer
	0, // 2: grpc.go.profiling.v1alpha.Profiling.Enable:input_type -> grpc.go.profiling.v1alpha.EnableRequest
	2, // 3: grpc.go.profiling.v1alpha.Profiling.GetStreamStats:input_type -> grpc.go.profiling.v1alpha.GetStreamStatsRequest
	1, // 4: grpc.go.profiling.v1alpha.Profiling.Enable:output_type -> grpc.go.profiling.v1alpha.EnableResponse
	3, // 5: grpc.go.profiling.v1alpha.Profiling.GetStreamStats:output_type -> grpc.go.profiling.v1alpha.GetStreamStatsResponse
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_profiling_proto_service_proto_init() }
func file_profiling_proto_service_proto_init() {
	if File_profiling_proto_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_profiling_proto_service_proto_rawDesc), len(file_profiling_proto_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_profiling_proto_service_proto_goTypes,
		DependencyIndexes: file_profiling_proto_service_proto_depIdxs,
		MessageInfos:      file_profiling_proto_service_proto_msgTypes,
	}.Build()
	File_profiling_proto_service_proto = out.File
	file_profiling_proto_service_proto_goTypes = nil
	file_profiling_proto_service_proto_depIdxs = nil
}
